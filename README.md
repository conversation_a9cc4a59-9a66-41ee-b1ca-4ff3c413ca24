# OpenAI Codex Global Environment

This repository contains a global pixi environment setup for OpenAI Codex development with Node.js and NumPy.

## 🚀 Features

- **Python 3.8+** with NumPy for numerical computing
- **Node.js 18+** for JavaScript development
- **OpenAI Python library** for API access
- **Pixi package management** for reproducible environments
- **Global access script** for easy environment activation

## 📦 Installation

The environment is already set up and ready to use! All dependencies are managed through pixi.

## 🔧 Usage

### Global Script

Use the `codex-global.sh` script for easy access to the environment:

```bash
# Show available commands
./codex-global.sh

# Test the environment
./codex-global.sh test

# Start an interactive shell with the environment
./codex-global.sh shell

# Run a specific command in the environment
./codex-global.sh run python example_codex.py

# Start Node.js REPL
./codex-global.sh node

# Start Python REPL
./codex-global.sh python
```

### Direct Pixi Commands

You can also use pixi commands directly:

```bash
# Activate the environment
pixi shell

# Run a command in the environment
pixi run python example_codex.py
pixi run node --version

# Run predefined tasks
pixi run test-env
pixi run codex
```

## 🔑 OpenAI API Setup

To use the OpenAI API, set your API key as an environment variable:

```bash
export OPENAI_API_KEY='your-api-key-here'
```

You can add this to your shell profile (`.bashrc`, `.zshrc`, etc.) to make it persistent.

## 📁 Project Structure

```
codex/
├── pixi.toml              # Pixi project configuration
├── pixi.lock              # Locked dependencies
├── codex-global.sh        # Global access script
├── example_codex.py       # Example demonstration script
├── scripts/
│   └── activate.sh        # Environment activation script
└── README.md              # This file
```

## 🛠 Available Tools

### Python Environment
- **Python**: 3.8+
- **NumPy**: Latest version for numerical computing
- **OpenAI**: Latest Python library for API access
- **Pip**: For additional Python packages

### Node.js Environment
- **Node.js**: 18+ with npm included
- Full JavaScript/TypeScript development capabilities

## 📋 Predefined Tasks

The following tasks are available via `pixi run <task>`:

- `test-env`: Test that all components are working
- `codex`: Quick environment check
- `setup`: Install additional dependencies

## 🔄 Making it Default

To make this environment easily accessible from anywhere, you can:

1. **Add to PATH**: Add the codex directory to your PATH
   ```bash
   echo 'export PATH="$PATH:/path/to/codex"' >> ~/.bashrc
   ```

2. **Create an alias**: Add an alias to your shell profile
   ```bash
   echo 'alias codex="/path/to/codex/codex-global.sh"' >> ~/.bashrc
   ```

3. **Symlink to bin**: Create a symlink in a directory that's already in PATH
   ```bash
   ln -s /path/to/codex/codex-global.sh /usr/local/bin/codex
   ```

## 🧪 Testing

Run the example script to verify everything is working:

```bash
./codex-global.sh run python example_codex.py
```

This will test:
- NumPy functionality
- OpenAI library import
- Environment configuration

## 🔧 Customization

You can modify `pixi.toml` to add more dependencies or change versions. After making changes, run:

```bash
pixi install
```

## 📚 Resources

- [Pixi Documentation](https://pixi.sh/)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [NumPy Documentation](https://numpy.org/doc/)
- [Node.js Documentation](https://nodejs.org/docs/)

## 🤝 Contributing

Feel free to modify and extend this environment for your specific needs!
