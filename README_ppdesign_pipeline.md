**Guide RNA Design Pipeline (ppdesign)**

- Input FASTA: `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/pathdb.fna`
- Output dir: `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1`
- Runner: `pixi run` tasks if available, or direct CLIs (`ppdesign-grna`, `validate-grna`, `validate-grna-thermo`)

**Overview**
- Detect conserved protospacer targets across sequences in a multi-FASTA.
- Design guides under GC and PAM constraints with sequence clustering.
- Validate via alignment statistics and thermodynamic stability.
- Produce `guide_rnas.csv` and `guide_rnas.fasta` plus validation summaries in the output directory.

**Prerequisites**
- Tools available via Pixi tasks or on PATH:
  - `ppdesign-grna`
  - `validate-grna`
  - `validate-grna-thermo`
- Recommended PAM: `spCas9` (NGG). Adjust as needed.

**Quick Start**
- One-line run with defaults and explicit paths:
  - `bash scripts/run_ppdesign_pipeline.sh /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/pathdb.fna /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1`

**What the script does**
- Step 1: Guide design with conservation and clustering
  - `--conservation 0.70`
  - `--min-gc 40 --max-gc 60`
  - `--cluster-threshold 0.85`
  - `--min-guides 5 --max-guides 20`
  - `--pam-type spCas9`
  - `--no-degenerate`
  - `--min-additional-coverage 0.01`
  - `--required-targets "NC_003630_1|Pepper_mild_mottle_virus"`
- Step 2: Validation against targets (alignment statistics)
- Step 3: Thermodynamic analysis under CRISPR-like buffer conditions

**Exact Commands (standalone)**
- Create output directory (optional; the script also creates it):
  - `mkdir -p /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1`
- Design guides:
  - `pixi run ppdesign-grna \`
    `  --fasta-input /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/pathdb.fna \`
    `  --output-dir /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1 \`
    `  --conservation 0.70 \`
    `  --no-degenerate \`
    `  --min-guides 5 \`
    `  --max-guides 20 \`
    `  --pam-type spCas9 \`
    `  --min-gc 40 --max-gc 60 \`
    `  --cluster-threshold 0.85 \`
    `  --min-additional-coverage 0.01 \`
    `  --required-targets "NC_003630_1|Pepper_mild_mottle_virus"`
- Validate guides:
  - `pixi run validate-grna \`
    `  --guide-csv /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1/guide_rnas.csv \`
    `  --guide-fasta /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1/guide_rnas.fasta \`
    `  --target-fasta /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/pathdb.fna`
- Thermodynamics:
  - `pixi run validate-grna-thermo \`
    `  --guide-csv /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1/guide_rnas.csv \`
    `  --guide-fasta /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1/guide_rnas.fasta \`
    `  --target-fasta /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/pathdb.fna`

If Pixi tasks are not configured on your machine, replace `pixi run <task>` with the bare CLI names `ppdesign-grna`, `validate-grna`, and `validate-grna-thermo` assuming they are installed in your environment.

**Outputs (all under the output dir)**
- `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1/guide_rnas.csv`
- `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1/guide_rnas.fasta`
- Validation summaries/files produced by `validate-grna` and `validate-grna-thermo` in the same directory.

**Parameter Guidance (senior bioinformatician notes)**
- Conservation (0.7): favors targets present across ≥70% of sequences; increase to 0.8–0.9 for maximum robustness if guide counts remain above `--min-guides`.
- GC window (40–60%): balances melting temperature and specificity; keep PAM-proximal region free of strong homopolymers and avoid poly-T stretches (>4).
- Clustering (0.85): reduces redundancy while maintaining coverage; tighten to 0.9–0.95 for highly similar strains.
- PAM type: `spCas9` (NGG) is broadly compatible; switch PAM to match the nuclease if using alternatives (SaCas9, AsCas12a, etc.).
- Required targets: use to guarantee inclusion of clinically relevant strains or references.
- Additional coverage: set low (0.01) to allow incremental coverage gains when near completeness.

**Reproducible Run Example**
- `bash scripts/run_ppdesign_pipeline.sh \`
  `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/pathdb.fna \`
  `/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1`

Environment variables to tweak without editing the script:
- `CONSERVATION=0.8 MIN_GUIDES=8 MAX_GUIDES=25 CLUSTER_THRESHOLD=0.9 bash scripts/run_ppdesign_pipeline.sh /.../pathdb.fna /.../test1`

**Troubleshooting**
- If you see “command not found” for `ppdesign-grna`, ensure the ppdesign package is installed or available as a Pixi task in your `pixi.toml` for that project.
- Confirm the input FASTA exists and is readable; multi-FASTA is expected.
- Large multi-FASTA runs may require more memory; consider sharding by taxon.

**Next Steps**
- Downstream off-target screening against host/reference databases, if supported by your ppdesign build.
- Exploration notebook: run `pixi run jupyter-local` or `pixi run jupyter-tailscale` (if configured) and inspect results interactively.

