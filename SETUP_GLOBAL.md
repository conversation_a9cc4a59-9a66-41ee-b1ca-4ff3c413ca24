# Making OpenAI Codex Environment Globally Available

Follow these steps to make the OpenAI Codex environment accessible from anywhere on your system.

## Option 1: Add to PATH (Recommended)

Add the codex directory to your PATH so you can run `codex-global.sh` from anywhere:

```bash
# Add to your shell profile (choose the appropriate one for your shell)
echo 'export PATH="$PATH:'$(pwd)'"' >> ~/.bashrc    # For bash
echo 'export PATH="$PATH:'$(pwd)'"' >> ~/.zshrc     # For zsh

# Reload your shell configuration
source ~/.bashrc  # or source ~/.zshrc
```

After this, you can run the script from anywhere:
```bash
codex-global.sh test
codex-global.sh shell
```

## Option 2: Create a Global Alias

Create an alias for easier access:

```bash
# Add alias to your shell profile
echo 'alias codex="'$(pwd)'/codex-global.sh"' >> ~/.bashrc    # For bash
echo 'alias codex="'$(pwd)'/codex-global.sh"' >> ~/.zshrc     # For zsh

# Reload your shell configuration
source ~/.bashrc  # or source ~/.zshrc
```

After this, you can use the `codex` command:
```bash
codex test
codex shell
codex run python example_codex.py
```

## Option 3: System-wide Installation

For system-wide access (requires sudo):

```bash
# Create a symlink in /usr/local/bin
sudo ln -s $(pwd)/codex-global.sh /usr/local/bin/codex

# Make sure /usr/local/bin is in your PATH (usually it is by default)
```

After this, the `codex` command will be available system-wide.

## Option 4: User Binary Directory

For user-specific installation without sudo:

```bash
# Create user bin directory if it doesn't exist
mkdir -p ~/.local/bin

# Create symlink
ln -s $(pwd)/codex-global.sh ~/.local/bin/codex

# Add to PATH if not already there
echo 'export PATH="$PATH:$HOME/.local/bin"' >> ~/.bashrc
source ~/.bashrc
```

## Verification

After setting up global access, verify it works:

```bash
# Test from any directory
cd ~
codex test

# Should output:
# Testing OpenAI Codex environment...
# Environment ready! NumPy: 2.3.2, OpenAI: 1.106.1
```

## Setting Default Environment Variables

To make the environment even more convenient, you can set default environment variables:

```bash
# Add to your shell profile
echo 'export OPENAI_API_KEY="your-api-key-here"' >> ~/.bashrc
echo 'export CODEX_HOME="'$(pwd)'"' >> ~/.bashrc

# Reload
source ~/.bashrc
```

## Auto-activation on Shell Start

To automatically activate the pixi environment when starting a new shell (optional):

```bash
# Add to your shell profile
echo 'cd $CODEX_HOME && pixi shell' >> ~/.bashrc
```

**Note**: This will automatically start the pixi shell every time you open a terminal. Only do this if you primarily work with this environment.

## Usage Examples

Once globally set up, you can use the environment from anywhere:

```bash
# From any directory
codex test                           # Test environment
codex shell                          # Start interactive shell
codex run python -c "import numpy"  # Run Python with NumPy
codex node                           # Start Node.js REPL
codex python                         # Start Python REPL
```

## Troubleshooting

### Command not found
- Make sure the PATH is correctly set
- Verify the script is executable: `chmod +x codex-global.sh`
- Check that pixi is installed and in PATH

### Permission denied
- Make sure the script has execute permissions
- If using system-wide installation, ensure you have proper permissions

### Environment not activating
- Verify pixi is installed: `pixi --version`
- Check that you're in the correct directory when running the script
- Ensure `pixi.toml` exists in the codex directory
