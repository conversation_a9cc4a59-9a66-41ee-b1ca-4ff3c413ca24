#!/usr/bin/env python3
import os
import math
import csv
import statistics as stats
from collections import defaultdict, OrderedDict

HAVE_MPL = True
try:
    import matplotlib  # type: ignore
    matplotlib.use("Agg")
    import matplotlib.pyplot as plt  # type: ignore
except Exception:
    HAVE_MPL = False


INPUT_PATH = "/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/Hisat2/test"
OUT_DIR = os.path.join(os.path.dirname(__file__), "output")
os.makedirs(OUT_DIR, exist_ok=True)


def read_records(path):
    records = []
    with open(path, "r", newline="") as f:
        reader = csv.reader(f, delimiter='\t')
        header = None
        for row in reader:
            if not row or all((c or "").strip() == "" for c in row):
                continue
            # Detect header lines anywhere in the file
            if row[0] == "Sample" and "Copies" in row and "Treatment" in row:
                header = [c.strip() for c in row]
                continue
            if header is None:
                # If file starts without header, assume standard order
                header = ["Sample", "Copies", "Treatment"]
            # Map columns safely
            try:
                sample = row[0].strip()
                copies = int(row[1].strip())
                treatment = row[2].strip()
            except Exception:
                # Skip malformed lines
                continue
            records.append({
                "Sample": sample,
                "Copies": copies,
                "Treatment": treatment,
            })
    return records


def summarize_by_treatment(records):
    groups = defaultdict(list)
    for r in records:
        groups[r["Treatment"]].append(r["Copies"])

    # Prefer a biologically sensible display order if present
    preferred_order = ["No_treatment", "Control_Cas9", "guideRNA+Cas9"]
    treatments = [t for t in preferred_order if t in groups] + [
        t for t in sorted(groups) if t not in preferred_order
    ]

    def percentile(sorted_vals, p):
        n = len(sorted_vals)
        if n == 0:
            return float('nan')
        if n == 1:
            return sorted_vals[0]
        idx = p * (n - 1)
        lo = int(math.floor(idx))
        hi = int(math.ceil(idx))
        if lo == hi:
            return sorted_vals[lo]
        frac = idx - lo
        return sorted_vals[lo] * (1 - frac) + sorted_vals[hi] * frac

    summary = OrderedDict()
    for t in treatments:
        vals = sorted(groups[t])
        n = len(vals)
        mean = sum(vals) / n if n else float("nan")
        median = stats.median(vals) if n else float("nan")
        sd = stats.stdev(vals) if n >= 2 else float("nan")
        q1 = percentile(vals, 0.25) if n >= 2 else float("nan")
        q3 = percentile(vals, 0.75) if n >= 2 else float("nan")
        iqr = (q3 - q1) if (not math.isnan(q3) and not math.isnan(q1)) else float("nan")
        summary[t] = {
            "n": n,
            "mean": mean,
            "median": median,
            "sd": sd,
            "q1": q1,
            "q3": q3,
            "iqr": iqr,
            "min": min(vals) if n else float("nan"),
            "max": max(vals) if n else float("nan"),
        }
    return summary, groups, treatments


def save_summary_tsv(summary, out_path):
    with open(out_path, "w", newline="") as f:
        w = csv.writer(f, delimiter='\t')
        w.writerow(["Treatment", "n", "mean", "median", "sd", "q1", "q3", "iqr", "min", "max"])
        for t, s in summary.items():
            w.writerow([t, s["n"], s["mean"], s["median"], s["sd"], s["q1"], s["q3"], s["iqr"], s["min"], s["max"]])


def log10_list(values):
    out = []
    for v in values:
        if v <= 0:
            out.append(float("nan"))
        else:
            out.append(math.log10(v))
    return out


def make_plots(groups, treatments, summary, out_png, out_svg):
    if not HAVE_MPL:
        # Fallback: generate SVG manually
        make_svg_plots(groups, treatments, summary, out_svg)
        # Create a tiny text PNG placeholder note (not supported without PIL), so skip PNG in fallback
        return
    # Prepare data in treatment order
    data = [groups[t] for t in treatments]

    # Set style
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, axes = plt.subplots(1, 2, figsize=(13, 5), constrained_layout=True)

    # Left: Boxplot (log10 y-axis)
    ax = axes[0]
    # Use log10-transformed values for nicer boxplot geometry
    data_log = [log10_list(vals) for vals in data]
    bp = ax.boxplot(data_log, labels=treatments, showmeans=True, patch_artist=True)
    # Color boxes
    colors = ['#4C78A8', '#F58518', '#54A24B', '#B279A2', '#E45756', '#72B7B2']
    for patch, color in zip(bp['boxes'], colors * 3):
        patch.set(facecolor=color, alpha=0.6)
    ax.set_title('Copies per Treatment (log10 scale)')
    ax.set_ylabel('log10(Copies)')
    # Add n annotations below x-tick labels
    for i, t in enumerate(treatments, start=1):
        n = summary[t]['n']
        ax.text(i, ax.get_ylim()[0] + 0.05*(ax.get_ylim()[1]-ax.get_ylim()[0]), f"n={n}",
                ha='center', va='bottom', fontsize=9, color='dimgray')

    # Right: Bar chart of median with IQR
    ax2 = axes[1]
    medians = [summary[t]['median'] for t in treatments]
    iqrs = [summary[t]['iqr'] for t in treatments]
    # Convert to log10 for comparability with left plot
    med_log = [math.log10(v) if v > 0 else float('nan') for v in medians]
    # IQR on log scale: approximate by transforming quartiles
    q1s = [summary[t]['q1'] for t in treatments]
    q3s = [summary[t]['q3'] for t in treatments]
    q1_log = [math.log10(v) if v > 0 else float('nan') for v in q1s]
    q3_log = [math.log10(v) if v > 0 else float('nan') for v in q3s]
    err_low = [m - q1 for m, q1 in zip(med_log, q1_log)]
    err_high = [q3 - m for q3, m in zip(q3_log, med_log)]
    x = list(range(len(treatments)))
    bars = ax2.bar(x, med_log, yerr=[err_low, err_high], color=colors[:len(treatments)], alpha=0.8, capsize=5)
    ax2.set_xticks(x)
    ax2.set_xticklabels(treatments)
    ax2.set_ylabel('log10(Copies)')
    ax2.set_title('Overall (median ± IQR)')
    # Annotate n above bars
    for xi, t, b in zip(x, treatments, bars):
        ax2.text(b.get_x() + b.get_width()/2, b.get_height(), f"n={summary[t]['n']}",
                 ha='center', va='bottom', fontsize=9, color='dimgray', xytext=(0, 4), textcoords='offset points')

    # Add compact caption
    cap_lines = [
        "Summary: median and IQR shown on log10 scale.",
        "Left: distributions per treatment. Right: central tendency per treatment.",
    ]
    fig.suptitle("Hisat2 test: Copies by Treatment", fontsize=14, y=1.02)
    fig.text(0.5, -0.02, " ".join(cap_lines), ha='center', va='top', fontsize=9, color='dimgray')

    # Save outputs
    fig.savefig(out_png, dpi=200, bbox_inches='tight')
    fig.savefig(out_svg, bbox_inches='tight')
    plt.close(fig)


def make_svg_plots(groups, treatments, summary, out_svg):
    # Simple hand-rolled SVG: two panels side by side
    W, H = 1000, 420
    margin = 60
    panel_gap = 40
    panel_w = (W - 2*margin - panel_gap) // 2
    panel_h = H - 2*margin

    # Utility functions
    def svg_header():
        return [f"<svg xmlns='http://www.w3.org/2000/svg' width='{W}' height='{H}'>",
                f"<rect x='0' y='0' width='{W}' height='{H}' fill='white' />"]
    def svg_footer():
        return ["</svg>"]
    def text(x, y, s, size=12, anchor='start', color='#333'):
        return f"<text x='{x}' y='{y}' font-size='{size}' fill='{color}' text-anchor='{anchor}' font-family='Arial, Helvetica, sans-serif'>{s}</text>"
    def line(x1,y1,x2,y2,color='#999',width=1):
        return f"<line x1='{x1}' y1='{y1}' x2='{x2}' y2='{y2}' stroke='{color}' stroke-width='{width}' />"
    def rect(x,y,w,h,fill='#4C78A8',opacity=0.6,stroke='#333'):
        return f"<rect x='{x}' y='{y}' width='{w}' height='{h}' fill='{fill}' fill-opacity='{opacity}' stroke='{stroke}' stroke-width='1' />"

    # Y scale: log10 of all values
    all_vals = [v for t in treatments for v in groups[t] if v > 0]
    log_vals = [math.log10(v) for v in all_vals]
    y_min = math.floor(min(log_vals))
    y_max = math.ceil(max(log_vals))
    def y_to_svg(logy, top, height):
        # logy in [y_min, y_max] -> y pixel
        return top + height * (1 - (logy - y_min) / (y_max - y_min or 1))

    colors = ['#4C78A8', '#F58518', '#54A24B', '#B279A2', '#E45756', '#72B7B2']

    out = []
    out += svg_header()
    # Titles
    out.append(text(W/2, 24, "Hisat2 test: Copies by Treatment", size=16, anchor='middle'))
    out.append(text(W/2, H-8, "Summary: median and IQR shown on log10 scale. Left: distributions; Right: medians.", size=12, anchor='middle', color='#666'))

    # Panel 1: Boxplot
    p1x, p1y = margin, margin
    out.append(line(p1x, p1y+panel_h, p1x+panel_w, p1y+panel_h, '#444', 1.2))
    # Y grid + ticks
    for lv in range(y_min, y_max+1):
        yy = y_to_svg(lv, p1y, panel_h)
        out.append(line(p1x, yy, p1x+panel_w, yy, '#eee', 1))
        out.append(text(p1x-8, yy+4, f"10^{lv}", size=10, anchor='end', color='#555'))
    out.append(text(p1x + panel_w/2, p1y - 12, 'Copies per Treatment (log10 scale)', size=13, anchor='middle'))
    # Draw boxplots
    k = len(treatments)
    if k:
        slot_w = panel_w / k
        box_w = slot_w * 0.45
        for i, t in enumerate(treatments):
            vals = sorted([v for v in groups[t] if v > 0])
            if not vals:
                continue
            # quartiles and whiskers (Tukey)
            def percentile(sorted_vals, p):
                n = len(sorted_vals)
                if n == 0:
                    return float('nan')
                if n == 1:
                    return sorted_vals[0]
                idx = p * (n - 1)
                lo = int(math.floor(idx))
                hi = int(math.ceil(idx))
                if lo == hi:
                    return sorted_vals[lo]
                frac = idx - lo
                return sorted_vals[lo] * (1 - frac) + sorted_vals[hi] * frac

            q1 = percentile(vals, 0.25) if len(vals) >= 2 else vals[0]
            q3 = percentile(vals, 0.75) if len(vals) >= 2 else vals[-1]
            med = stats.median(vals)
            iqr = q3 - q1
            lo = max(min(vals), q1 - 1.5*iqr)
            hi = min(max(vals), q3 + 1.5*iqr)
            # positions
            cx = p1x + slot_w*(i+0.5)
            bx = cx - box_w/2
            q1y = y_to_svg(math.log10(q1), p1y, panel_h)
            q3y = y_to_svg(math.log10(q3), p1y, panel_h)
            medy = y_to_svg(math.log10(med), p1y, panel_h)
            loy = y_to_svg(math.log10(lo), p1y, panel_h)
            hiy = y_to_svg(math.log10(hi), p1y, panel_h)
            color = colors[i % len(colors)]
            out.append(rect(bx, q3y, box_w, max(1, q1y-q3y), fill=color, opacity=0.6))
            out.append(line(bx, medy, bx+box_w, medy, '#222', 2))
            out.append(line(cx, q3y, cx, hiy, '#777', 1))
            out.append(line(cx, q1y, cx, loy, '#777', 1))
            out.append(line(cx - box_w*0.25, hiy, cx + box_w*0.25, hiy, '#777', 1))
            out.append(line(cx - box_w*0.25, loy, cx + box_w*0.25, loy, '#777', 1))
            # x label and n
            out.append(text(cx, p1y+panel_h+18, t, size=11, anchor='middle'))
            out.append(text(cx, p1y+panel_h+32, f"n={summary[t]['n']}", size=10, anchor='middle', color='#666'))

    # Panel 2: Bar chart of median with IQR (log scale via transforming quartiles)
    p2x = margin + panel_w + panel_gap
    p2y = margin
    out.append(line(p2x, p2y+panel_h, p2x+panel_w, p2y+panel_h, '#444', 1.2))
    for lv in range(y_min, y_max+1):
        yy = y_to_svg(lv, p2y, panel_h)
        out.append(line(p2x, yy, p2x+panel_w, yy, '#eee', 1))
        out.append(text(p2x-8, yy+4, f"10^{lv}", size=10, anchor='end', color='#555'))
    out.append(text(p2x + panel_w/2, p2y - 12, 'Overall (median ± IQR)', size=13, anchor='middle'))
    if k:
        slot_w = panel_w / k
        bar_w = slot_w * 0.45
        for i, t in enumerate(treatments):
            q1 = summary[t]['q1']
            med = summary[t]['median']
            q3 = summary[t]['q3']
            if not (q1 and med and q3) or min(q1, med, q3) <= 0:
                continue
            cx = p2x + slot_w*(i+0.5)
            bx = cx - bar_w/2
            basey = y_to_svg(y_min, p2y, panel_h)
            medy = y_to_svg(math.log10(med), p2y, panel_h)
            top = min(basey, medy)
            height = abs(medy - basey)
            color = colors[i % len(colors)]
            out.append(rect(bx, top, bar_w, max(1, height), fill=color, opacity=0.8))
            # error bars (IQR)
            q1y = y_to_svg(math.log10(q1), p2y, panel_h)
            q3y = y_to_svg(math.log10(q3), p2y, panel_h)
            out.append(line(cx, q3y, cx, q1y, '#222', 1.5))
            out.append(line(cx - bar_w*0.25, q3y, cx + bar_w*0.25, q3y, '#222', 1.5))
            out.append(line(cx - bar_w*0.25, q1y, cx + bar_w*0.25, q1y, '#222', 1.5))
            out.append(text(cx, min(q3y, q1y) - 6, f"n={summary[t]['n']}", size=10, anchor='middle', color='#666'))

    # Axis labels
    out.append(text(margin - 38, margin + panel_h/2, 'log10(Copies)', size=12, anchor='middle', color='#333'))
    out.append(text(W - margin - panel_w - 38, margin + panel_h/2, 'log10(Copies)', size=12, anchor='middle', color='#333'))

    out += svg_footer()
    with open(out_svg, 'w') as f:
        f.write("\n".join(out))


def main():
    records = read_records(INPUT_PATH)
    if not records:
        raise SystemExit("No records found in input file.")
    summary, groups, treatments = summarize_by_treatment(records)
    # Save summary TSV
    out_tsv = os.path.join(OUT_DIR, "hisat2_test_summary.tsv")
    save_summary_tsv(summary, out_tsv)
    # Make figure
    out_png = os.path.join(OUT_DIR, "hisat2_test_plots.png")
    out_svg = os.path.join(OUT_DIR, "hisat2_test_plots.svg")
    make_plots(groups, treatments, summary, out_png, out_svg)
    # Also print a short textual summary to stdout
    print("Treatments:")
    for t, s in summary.items():
        # Compute fold-change vs No_treatment if available
        ref = summary.get("No_treatment", {}).get("median", float("nan"))
        fc = (s["median"] / ref) if (not math.isnan(ref) and ref != 0) else float("nan")
        print(f"- {t}: n={s['n']}, median={s['median']:.3g}, mean={s['mean']:.3g}, IQR={s['iqr']:.3g}, fold-vs-No_treatment={fc:.3g}")
    print(f"Saved: {out_png}\nSaved: {out_svg}\nSummary TSV: {out_tsv}")


if __name__ == "__main__":
    main()
