#!/bin/bash
# Global script to run OpenAI Codex with pixi environment

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to the codex directory
cd "$SCRIPT_DIR"

# Check if pixi is available
if ! command -v pixi &> /dev/null; then
    echo "Error: pixi is not installed or not in PATH"
    echo "Please install pixi first: https://pixi.sh/latest/"
    exit 1
fi

# Check if we have arguments
if [ $# -eq 0 ]; then
    echo "Starting OpenAI Codex environment..."
    echo "Available commands:"
    echo "  codex-global.sh shell    - Start an interactive shell with the environment"
    echo "  codex-global.sh run <cmd> - Run a command in the environment"
    echo "  codex-global.sh test     - Test the environment"
    echo "  codex-global.sh node     - Start Node.js REPL"
    echo "  codex-global.sh python   - Start Python REPL"
    exit 0
fi

case "$1" in
    "shell")
        echo "Starting interactive shell with OpenAI Codex environment..."
        pixi shell
        ;;
    "run")
        shift
        pixi run "$@"
        ;;
    "test")
        echo "Testing OpenAI Codex environment..."
        pixi run test-env
        ;;
    "node")
        echo "Starting Node.js REPL..."
        pixi run node
        ;;
    "python")
        echo "Starting Python REPL..."
        pixi run python
        ;;
    *)
        echo "Running command in OpenAI Codex environment: $@"
        pixi run "$@"
        ;;
esac
