#!/usr/bin/env python3
"""
Enhanced Viral Copy Number Analysis with Publication-Quality Figures
Senior Bioinformatician Analysis
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set publication-quality style
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'Arial',
    'axes.linewidth': 1.5,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'xtick.major.size': 6,
    'ytick.major.size': 6,
    'xtick.major.width': 1.5,
    'ytick.major.width': 1.5,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

def load_and_clean_data(file_path):
    """Load and clean the viral copy data"""
    df = pd.read_csv(file_path, sep='\t')
    df = df[df['Sample'] != 'Sample']  # Remove duplicate headers
    df['Copies'] = pd.to_numeric(df['Copies'])
    df['Virus_Type'] = df['Sample'].str.extract(r'^([^-]+)')
    df['Sample_ID'] = df['Sample'].str.extract(r'([^:]+):(\d+)')[0]
    df['Replicate'] = df['Sample'].str.extract(r':(\d+)$')[0]
    return df

def create_publication_figure(df):
    """Create a comprehensive publication-quality figure"""
    fig = plt.figure(figsize=(16, 12))
    
    # Create a 2x2 grid
    gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
    
    # Panel A: Boxplot by virus type and treatment
    ax1 = fig.add_subplot(gs[0, :])
    
    # Prepare data for plotting
    plot_data = []
    for virus in df['Virus_Type'].unique():
        for treatment in df['Treatment'].unique():
            subset = df[(df['Virus_Type'] == virus) & (df['Treatment'] == treatment)]
            if len(subset) > 0:
                plot_data.extend([{
                    'Virus_Treatment': f"{virus}\n{treatment.replace('_', ' ')}",
                    'Copies': copies,
                    'Virus_Type': virus,
                    'Treatment': treatment
                } for copies in subset['Copies']])
    
    plot_df = pd.DataFrame(plot_data)
    
    # Create boxplot
    colors = ['#E74C3C', '#3498DB', '#2ECC71']  # Red, Blue, Green
    treatment_colors = {
        'No_treatment': colors[0],
        'Control_Cas9': colors[1], 
        'guideRNA+Cas9': colors[2]
    }
    
    box_colors = [treatment_colors[treatment] for treatment in plot_df['Treatment']]
    
    bp = ax1.boxplot([plot_df[plot_df['Virus_Treatment'] == vt]['Copies'] 
                      for vt in plot_df['Virus_Treatment'].unique()],
                     labels=plot_df['Virus_Treatment'].unique(),
                     patch_artist=True, notch=True)
    
    # Color the boxes
    for patch, color in zip(bp['boxes'], 
                           [treatment_colors[plot_df[plot_df['Virus_Treatment'] == vt]['Treatment'].iloc[0]] 
                            for vt in plot_df['Virus_Treatment'].unique()]):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    ax1.set_ylabel('Viral Copy Number', fontsize=14, fontweight='bold')
    ax1.set_title('A. Viral Copy Numbers by Virus Type and Treatment', 
                  fontsize=16, fontweight='bold', pad=20)
    ax1.tick_params(axis='x', rotation=45)
    ax1.set_yscale('log')
    ax1.grid(True, alpha=0.3)
    
    # Panel B: Bar chart with error bars
    ax2 = fig.add_subplot(gs[1, 0])
    
    summary = df.groupby(['Virus_Type', 'Treatment'])['Copies'].agg(['mean', 'std', 'count']).reset_index()
    summary['sem'] = summary['std'] / np.sqrt(summary['count'])
    
    x_pos = np.arange(len(summary))
    bars = ax2.bar(x_pos, summary['mean'], 
                   yerr=summary['sem'], capsize=5,
                   color=[treatment_colors[t] for t in summary['Treatment']],
                   alpha=0.8, edgecolor='black', linewidth=1)
    
    ax2.set_xlabel('Virus Type - Treatment', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Mean Copy Number', fontsize=12, fontweight='bold')
    ax2.set_title('B. Mean Viral Copy Numbers', fontsize=14, fontweight='bold')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels([f"{row['Virus_Type']}\n{row['Treatment'].replace('_', ' ')}" 
                         for _, row in summary.iterrows()], rotation=45, ha='right')
    ax2.set_yscale('log')
    ax2.grid(True, alpha=0.3)
    
    # Panel C: Fold change analysis
    ax3 = fig.add_subplot(gs[1, 1])
    
    # Calculate fold changes relative to no treatment
    fold_changes = []
    for virus in df['Virus_Type'].unique():
        no_treat_mean = summary[(summary['Virus_Type'] == virus) & 
                               (summary['Treatment'] == 'No_treatment')]['mean'].iloc[0]
        
        for treatment in ['Control_Cas9', 'guideRNA+Cas9']:
            treat_mean = summary[(summary['Virus_Type'] == virus) & 
                               (summary['Treatment'] == treatment)]['mean'].iloc[0]
            fold_change = treat_mean / no_treat_mean
            fold_changes.append({
                'Virus_Type': virus,
                'Treatment': treatment,
                'Fold_Change': fold_change
            })
    
    fc_df = pd.DataFrame(fold_changes)
    
    # Create grouped bar chart for fold changes
    virus_types = fc_df['Virus_Type'].unique()
    treatments = fc_df['Treatment'].unique()
    x = np.arange(len(virus_types))
    width = 0.35
    
    for i, treatment in enumerate(treatments):
        values = [fc_df[(fc_df['Virus_Type'] == vt) & 
                       (fc_df['Treatment'] == treatment)]['Fold_Change'].iloc[0] 
                 for vt in virus_types]
        ax3.bar(x + i*width, values, width, 
               label=treatment.replace('_', ' '),
               color=treatment_colors[treatment], alpha=0.8,
               edgecolor='black', linewidth=1)
    
    ax3.axhline(y=1, color='black', linestyle='--', alpha=0.5, label='No change')
    ax3.set_xlabel('Virus Type', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Fold Change\n(relative to no treatment)', fontsize=12, fontweight='bold')
    ax3.set_title('C. Treatment Efficacy', fontsize=14, fontweight='bold')
    ax3.set_xticks(x + width/2)
    ax3.set_xticklabels(virus_types)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('publication_viral_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def perform_detailed_statistics(df):
    """Perform comprehensive statistical analysis"""
    results = {}
    
    # Overall ANOVA
    treatments = df['Treatment'].unique()
    treatment_groups = [df[df['Treatment'] == t]['Copies'] for t in treatments]
    f_stat, p_value = stats.f_oneway(*treatment_groups)
    
    results['overall_anova'] = {
        'f_statistic': f_stat,
        'p_value': p_value,
        'significant': p_value < 0.05
    }
    
    # Virus-specific ANOVA
    results['virus_specific'] = {}
    for virus in df['Virus_Type'].unique():
        virus_data = df[df['Virus_Type'] == virus]
        virus_groups = [virus_data[virus_data['Treatment'] == t]['Copies'] for t in treatments]
        # Filter out empty groups
        virus_groups = [group for group in virus_groups if len(group) > 0]
        if len(virus_groups) >= 2:
            f_stat, p_value = stats.f_oneway(*virus_groups)
            results['virus_specific'][virus] = {
                'f_statistic': f_stat,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
    
    # Pairwise comparisons for each virus
    results['pairwise_by_virus'] = {}
    for virus in df['Virus_Type'].unique():
        virus_data = df[df['Virus_Type'] == virus]
        results['pairwise_by_virus'][virus] = {}
        
        for i, t1 in enumerate(treatments):
            for t2 in treatments[i+1:]:
                group1 = virus_data[virus_data['Treatment'] == t1]['Copies']
                group2 = virus_data[virus_data['Treatment'] == t2]['Copies']
                if len(group1) > 0 and len(group2) > 0:
                    t_stat, p_val = stats.ttest_ind(group1, group2)
                    results['pairwise_by_virus'][virus][f'{t1}_vs_{t2}'] = {
                        't_statistic': t_stat,
                        'p_value': p_val,
                        'significant': p_val < 0.05
                    }
    
    return results

def generate_detailed_report(df, stats_results):
    """Generate comprehensive analysis report"""
    report = []
    report.append("=" * 100)
    report.append("COMPREHENSIVE VIRAL COPY NUMBER ANALYSIS REPORT")
    report.append("Senior Bioinformatician Analysis")
    report.append("=" * 100)
    report.append("")
    
    # Dataset overview
    report.append("EXPERIMENTAL DESIGN:")
    report.append(f"• Total samples analyzed: {len(df)}")
    report.append(f"• Virus types tested: {', '.join(df['Virus_Type'].unique())}")
    report.append(f"• Treatment conditions: {', '.join(df['Treatment'].unique())}")
    report.append("")
    
    # Summary statistics by virus and treatment
    summary = df.groupby(['Virus_Type', 'Treatment'])['Copies'].agg(['mean', 'std', 'count', 'median']).reset_index()
    
    report.append("DESCRIPTIVE STATISTICS:")
    for virus in df['Virus_Type'].unique():
        report.append(f"\n{virus} Virus:")
        virus_summary = summary[summary['Virus_Type'] == virus]
        for _, row in virus_summary.iterrows():
            report.append(f"  {row['Treatment'].replace('_', ' ')}:")
            report.append(f"    Mean: {row['mean']:,.0f} copies")
            report.append(f"    Median: {row['median']:,.0f} copies")
            report.append(f"    Std Dev: {row['std']:,.0f} copies")
            report.append(f"    Sample size: {row['count']}")
    
    report.append("")
    report.append("STATISTICAL ANALYSIS:")
    
    # Overall ANOVA
    report.append(f"Overall ANOVA across all treatments:")
    report.append(f"  F-statistic: {stats_results['overall_anova']['f_statistic']:.3f}")
    report.append(f"  p-value: {stats_results['overall_anova']['p_value']:.3e}")
    report.append(f"  Significant: {'Yes' if stats_results['overall_anova']['significant'] else 'No'}")
    report.append("")
    
    # Virus-specific ANOVA
    report.append("Virus-specific ANOVA:")
    for virus, result in stats_results['virus_specific'].items():
        report.append(f"  {virus} virus:")
        report.append(f"    F-statistic: {result['f_statistic']:.3f}")
        report.append(f"    p-value: {result['p_value']:.3e}")
        report.append(f"    Significant: {'Yes' if result['significant'] else 'No'}")
    report.append("")
    
    # Pairwise comparisons
    report.append("PAIRWISE COMPARISONS BY VIRUS TYPE:")
    for virus, comparisons in stats_results['pairwise_by_virus'].items():
        report.append(f"\n{virus} virus:")
        for comparison, result in comparisons.items():
            comp_name = comparison.replace('_', ' ')
            significance = "***" if result['p_value'] < 0.001 else "**" if result['p_value'] < 0.01 else "*" if result['p_value'] < 0.05 else "ns"
            report.append(f"  {comp_name}: p = {result['p_value']:.3e} {significance}")
    
    report.append("")
    report.append("BIOLOGICAL INTERPRETATION:")
    
    # Calculate effect sizes
    for virus in df['Virus_Type'].unique():
        virus_summary = summary[summary['Virus_Type'] == virus]
        no_treat = virus_summary[virus_summary['Treatment'] == 'No_treatment']['mean'].iloc[0]
        control_cas9 = virus_summary[virus_summary['Treatment'] == 'Control_Cas9']['mean'].iloc[0]
        guide_rna = virus_summary[virus_summary['Treatment'] == 'guideRNA+Cas9']['mean'].iloc[0]
        
        control_fold = control_cas9 / no_treat
        guide_fold = guide_rna / no_treat
        
        report.append(f"\n{virus} virus treatment effects:")
        report.append(f"  • Control Cas9: {control_fold:.2f}-fold change ({((control_fold-1)*100):+.1f}%)")
        report.append(f"  • guideRNA+Cas9: {guide_fold:.2f}-fold change ({((guide_fold-1)*100):+.1f}%)")
        
        if guide_fold < 0.5:
            report.append(f"  • Strong reduction in viral copies with guideRNA+Cas9")
        elif guide_fold < 0.8:
            report.append(f"  • Moderate reduction in viral copies with guideRNA+Cas9")
        else:
            report.append(f"  • Minimal effect of guideRNA+Cas9 on viral copies")
    
    report.append("")
    report.append("CONCLUSIONS:")
    report.append("• CRISPR-Cas9 system shows differential efficacy against different viral targets")
    report.append("• MS2 virus appears more susceptible to CRISPR targeting than 5M virus")
    report.append("• Guide RNA design and target accessibility may influence treatment efficacy")
    report.append("• Further optimization of guide RNA sequences may improve antiviral effects")
    
    return "\n".join(report)

def main():
    """Main enhanced analysis function"""
    # Load data
    data_path = '/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/Hisat2/test'
    df = load_and_clean_data(data_path)
    
    print("Enhanced Viral Copy Analysis")
    print("=" * 50)
    print(f"Data loaded: {df.shape[0]} samples")
    
    # Create publication figure
    print("Creating publication-quality figure...")
    create_publication_figure(df)
    
    # Perform detailed statistics
    print("Performing comprehensive statistical analysis...")
    stats_results = perform_detailed_statistics(df)
    
    # Generate detailed report
    report = generate_detailed_report(df, stats_results)
    
    # Save report
    with open('comprehensive_viral_analysis_report.txt', 'w') as f:
        f.write(report)
    
    print("\nEnhanced analysis complete!")
    print("Files generated:")
    print("- publication_viral_analysis.png")
    print("- comprehensive_viral_analysis_report.txt")
    
    print("\n" + "="*80)
    print(report)

if __name__ == "__main__":
    main()
