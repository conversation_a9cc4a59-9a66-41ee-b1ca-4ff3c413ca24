#!/usr/bin/env python3
"""
Example script demonstrating OpenAI Codex environment with NumPy
"""

import numpy as np
import openai
import os
from typing import List, Optional

def test_numpy_functionality():
    """Test NumPy functionality"""
    print("=== NumPy Test ===")
    print(f"NumPy version: {np.__version__}")
    
    # Create some test arrays
    arr1 = np.array([1, 2, 3, 4, 5])
    arr2 = np.array([2, 4, 6, 8, 10])
    
    print(f"Array 1: {arr1}")
    print(f"Array 2: {arr2}")
    print(f"Sum: {arr1 + arr2}")
    print(f"Mean of Array 1: {np.mean(arr1)}")
    print(f"Standard deviation of Array 2: {np.std(arr2)}")
    
    # Matrix operations
    matrix = np.random.rand(3, 3)
    print(f"Random 3x3 matrix:\n{matrix}")
    print(f"Matrix determinant: {np.linalg.det(matrix)}")
    print()

def test_openai_setup():
    """Test OpenAI setup"""
    print("=== OpenAI Test ===")
    print(f"OpenAI version: {openai.__version__}")
    
    # Check if API key is set
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print("✓ OPENAI_API_KEY environment variable is set")
        print(f"API Key (first 10 chars): {api_key[:10]}...")
    else:
        print("⚠ OPENAI_API_KEY environment variable is not set")
        print("To use OpenAI API, set your API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
    
    print("OpenAI client initialized successfully")
    print()

def demonstrate_environment():
    """Demonstrate the complete environment"""
    print("🚀 OpenAI Codex Environment Demo")
    print("=" * 50)
    
    test_numpy_functionality()
    test_openai_setup()
    
    print("=== Environment Summary ===")
    print("✓ Python environment ready")
    print("✓ NumPy for numerical computing")
    print("✓ OpenAI library for API access")
    print("✓ Node.js available for JavaScript development")
    print()
    print("Environment is ready for OpenAI Codex development!")
    print("=" * 50)

if __name__ == "__main__":
    demonstrate_environment()
