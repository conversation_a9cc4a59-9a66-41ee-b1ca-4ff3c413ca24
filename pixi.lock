version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.anaconda.org/pytorch/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/argon2-cffi-bindings-25.1.0-py311h49ec1c0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.5-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb03c661_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb03c661_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py311h1ddb823_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.8.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py311h5b438cf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.3-py311hdf67eae_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cyrus-sasl-2.1.28-hd9c7081_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/debugpy-1.8.16-py311hc665b79_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/double-conversion-3.3.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.59.2-py311h3778330_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.14.1-ha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-hecca717_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.3.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.4.5-h15599e2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh82676e8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.5.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/jsonpointer-3.0.0-py311h38be061_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.9.1-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.1-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-1.1.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.3.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_console-6.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.17.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.9-py311h724c32c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-35_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb03c661_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb03c661_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb03c661_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-35_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp20.1-20.1.8-default_h99862b1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang13-21.1.0-default_h746c552_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-hb8b1518_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb03c661_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.14.1-ha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.14.1-h73754d4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.3-hf39c6af_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h3b78370_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-35_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm20-20.1.8-hecd9e04_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm21-21.1.0-hecd9e04_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libntlm-1.8-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopengl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h421ea60_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpq-17.6-h3675c94_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-h8261f1e_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb03c661_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.6.0-hd42ef1d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.11.0-he8b52b9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h04c0eec_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxslt-1.1.43-h7a3aeb2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py311h2dc5d0c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-3.10.6-py311h38be061_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.6-py311h0f3be63_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.4-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nodejs-24.4.1-heeeca48_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-7.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py311h2e04523_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h55fea9a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openldap-2.6.10-he970967_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.2-h26f9b46_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.2-py311hed34c8f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.5-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.3.0-py311h3df08e7_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.4-h54a6638_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.4.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.52-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt_toolkit-3.0.52-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py311h49ec1c0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.4-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyside6-6.9.2-py311h72d58bf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.11.13-h9e4cc4f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.2-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py311h2dc5d0c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.1.0-py311h2315fbb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qt6-main-6.9.2-h3fc9a0a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rpds-py-0.27.1-py311h902ca64_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.16.2-py311h1e13796_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh0d859eb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/statsmodels-0.14.5-py311hb0beb2c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh0d859eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.2-py311h49ec1c0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250822-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/unicodedata2-16.0.0-py311h49ec1c0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.24.0-h3e06ad9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.1-h4f16b4b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-cursor-0.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-hb711507_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.1-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.10-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.2-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcomposite-0.4.6-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdamage-1.1.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxi-1.8.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrandr-1.5.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxtst-1.2.5-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxxf86vm-1.1.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h280c20c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h387f397_9.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.25.0-py311haee01d2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/81/5a/0e73541b6edd3f4aada586c24e50626c7815c561a7ba337d6a7eb0a915b4/jiter-0.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/bc/cd720e078576bdb8255d5032c5d63ee5c0bf4b7173dd955185a1d658c456/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      osx-64:
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/appnope-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/argon2-cffi-bindings-25.1.0-py311h13e5629_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.5-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-1.1.0-h1c43f85_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-bin-1.1.0-h1c43f85_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-python-1.1.0-py311h7b20566_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/bzip2-1.0.8-hfdf4475_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.8.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/cffi-1.17.1-py311he66fa18_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/contourpy-1.3.3-py311hd4d69bb_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/debugpy-1.8.16-py311hc651eee_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/fonttools-4.59.2-py311hfbe4617_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/freetype-2.14.1-h694c41f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.3.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/icu-75.1-h120a0e1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh92f572d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.5.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/jsonpointer-3.0.0-py311h6eed73b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.9.1-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.1-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-1.1.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.3.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_console-6.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.17.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/kiwisolver-1.4.9-py311ha94bed4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/krb5-1.21.3-h37d8d59_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/lcms2-2.17-h72f5680_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/lerc-4.0.0-hcca01a6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libblas-3.9.0-35_h7f60823_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlicommon-1.1.0-h1c43f85_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlidec-1.1.0-h1c43f85_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlienc-1.1.0-h1c43f85_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcblas-3.9.0-35_hff6cab4_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcxx-21.1.0-h3d58e20_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libdeflate-1.24-hcc1b750_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libedit-3.1.20250104-pl5321ha958ccf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libexpat-2.7.1-h21dd04a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libffi-3.4.6-h281671d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype-2.14.1-h694c41f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype6-2.14.1-h6912278_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran-15.1.0-h5f6db21_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran5-15.1.0-hfa3c126_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libjpeg-turbo-3.1.0-h6e16a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblapack-3.9.0-35_h236ab99_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblzma-5.8.1-hd471939_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libopenblas-0.3.30-openmp_h83c2472_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libpng-1.6.50-h84aeda2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libsodium-1.0.20-hfdf4475_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libsqlite-3.50.4-h39a8b3b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libtiff-4.7.0-h59ddb5d_6.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libuv-1.51.0-h58003a5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libwebp-base-1.6.0-hb807250_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libxcb-1.17.0-hf1f96e2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libzlib-1.3.1-hd23fc13_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/llvm-openmp-21.1.0-hf4e0ed4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/markupsafe-3.0.2-py311ha3cf9ac_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-3.10.6-py311h6eed73b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-base-3.10.6-py311h48d7e91_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.4-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/ncurses-6.5-h0622a9a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/nodejs-24.4.1-h2e7699b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-7.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/numpy-2.3.2-py311h09fcace_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/openjpeg-2.5.3-h036ada5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/openssl-3.5.2-h6e31bce_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pandas-2.3.2-py311hf4bc098_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.5-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pillow-11.3.0-py311h0d39b4b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.4.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.52-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt_toolkit-3.0.52-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/psutil-7.0.0-py311h13e5629_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pthread-stubs-0.4-h00291cd_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-core-11.1-py311h2f44256_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-framework-cocoa-11.1-py311hbc8e8a3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.4-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/python-3.11.13-h9ccd52b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.2-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyyaml-6.0.2-py311ha3cf9ac_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyzmq-27.1.0-py311h0ab6910_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/qhull-2020.2-h3c5361c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/readline-8.2-h7cca4af_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/rpds-py-0.27.1-py311hd3d88a1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/scipy-1.16.2-py311h32c7e5c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh31c8845_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/statsmodels-0.14.5-py311ha2d3830_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh31c8845_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/tk-8.6.13-hf689a15_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/tornado-6.5.2-py311h13e5629_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250822-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/unicodedata2-16.0.0-py311h13e5629_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxau-1.0.12-h6e16a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxdmcp-1.1.5-h00291cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/yaml-0.2.5-h4132b18_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zeromq-4.3.5-h6c33b1e_9.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zstandard-0.25.0-py311h62e9434_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zstd-1.5.7-h8210216_2.conda
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/1b/dd/6cefc6bd68b1c3c979cecfa7029ab582b57690a31cd2f346c4d0ce7951b6/jiter-0.10.0-cp311-cp311-macosx_10_12_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3f/8d/71db63483d518cbbf290261a1fc2839d17ff89fce7089e08cad07ccfce67/pydantic_core-2.33.2-cp311-cp311-macosx_10_12_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      osx-arm64:
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/appnope-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/argon2-cffi-bindings-25.1.0-py311h3696347_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.5-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-1.1.0-h6caf38d_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-bin-1.1.0-h6caf38d_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-python-1.1.0-py311hf719da1_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.8.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/cffi-1.17.1-py311h146a0b8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/contourpy-1.3.3-py311h57a9ea7_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/debugpy-1.8.16-py311ha59bd64_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/fonttools-4.59.2-py311h2fe624c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/freetype-2.14.1-hce30654_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.3.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh92f572d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.5.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/jsonpointer-3.0.0-py311h267d04e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.9.1-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.1-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-1.1.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.3.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_console-6.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.17.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/kiwisolver-1.4.9-py311h63e5c0c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/lcms2-2.17-h7eeda09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/lerc-4.0.0-hd64df32_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-35_h10e41b3_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlicommon-1.1.0-h6caf38d_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlidec-1.1.0-h6caf38d_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlienc-1.1.0-h6caf38d_4.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-35_hb3479ef_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-21.1.0-hf598326_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libdeflate-1.24-h5773f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.1-hec049ff_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype-2.14.1-hce30654_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype6-2.14.1-h6da58f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-15.1.0-hfdf1602_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-15.1.0-hb74de2c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libjpeg-turbo-3.1.0-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-35_hc9a63f6_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_h60d53f8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libpng-1.6.50-h280e0eb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.4-h4237e3c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libtiff-4.7.0-h025e3ab_6.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libuv-1.51.0-h6caf38d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libwebp-base-1.6.0-h07db88b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libxcb-1.17.0-hdb1d25a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-21.1.0-hbb9b287_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/markupsafe-3.0.2-py311h4921393_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-3.10.6-py311ha1ab1f8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-base-3.10.6-py311h66dac5a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.4-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/nodejs-24.4.1-hab9d20b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-7.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.2-py311h0856f98_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openjpeg-2.5.3-h889cd5d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.2-he92f556_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pandas-2.3.2-py311hff7e5bb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.5-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pillow-11.3.0-py311h3f9ac88_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.4.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.52-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt_toolkit-3.0.52-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/psutil-7.0.0-py311h3696347_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pthread-stubs-0.4-hd74edd7_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-core-11.1-py311hf0763de_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-framework-cocoa-11.1-py311hc5b188e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.4-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.11.13-hc22306f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.2-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyyaml-6.0.2-py311h4921393_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.1.0-py311h13abfa4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/qhull-2020.2-h420ef59_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/rpds-py-0.27.1-py311h1c3fc1a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/scipy-1.16.2-py311h2734c94_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh31c8845_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/statsmodels-0.14.5-py311h9dc9093_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh31c8845_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.2-py311h3696347_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250822-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/unicodedata2-16.0.0-py311h3696347_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxau-1.0.12-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxdmcp-1.1.5-hd74edd7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/yaml-0.2.5-h925e9cb_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-h888dc83_9.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstandard-0.25.0-py311h5bb9006_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstd-1.5.7-h6491c7d_2.conda
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/be/cf/fc33f5159ce132be1d8dd57251a1ec7a631c7df4bd11e1cd198308c6ae32/jiter-0.10.0-cp311-cp311-macosx_11_0_arm64.whl
      - pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/24/2f/3cfa7244ae292dd850989f328722d2aef313f74ffc471184dc509e1e4e5a/pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      win-64:
      - conda: https://conda.anaconda.org/conda-forge/win-64/_openmp_mutex-4.5-2_gnu.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/argon2-cffi-bindings-25.1.0-py311h3485c13_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.5-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/brotli-1.1.0-hfd05255_4.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/brotli-bin-1.1.0-hfd05255_4.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/brotli-python-1.1.0-py311h3e6a449_4.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/bzip2-1.0.8-h2466b09_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-h4c7d964_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/cairo-1.18.4-h5782bbf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.8.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/cffi-1.17.1-py311h3485c13_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/contourpy-1.3.3-py311h3fd045d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.11.13-py311hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/debugpy-1.8.16-py311h5dfdfe8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/double-conversion-3.3.1-he0c23c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/fontconfig-2.15.0-h765892d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/fonttools-4.59.2-py311h3f79411_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/freetype-2.14.1-h57928b3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/graphite2-1.3.14-hac47afa_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.3.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/harfbuzz-11.4.5-h5f2951f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/icu-75.1-he0c23c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh3521513_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.5.0-pyh6be1c34_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/jsonpointer-3.0.0-py311h1ea47a8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.9.1-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.1-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-1.1.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.3.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_console-6.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh5737063_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.17.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/kiwisolver-1.4.9-py311h275cad7_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/krb5-1.21.3-hdf4eb48_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/lcms2-2.17-hbcf6048_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/lerc-4.0.0-h6470a55_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libblas-3.9.0-35_h5709861_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libbrotlicommon-1.1.0-hfd05255_4.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libbrotlidec-1.1.0-hfd05255_4.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libbrotlienc-1.1.0-hfd05255_4.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libcblas-3.9.0-35_h2a3cdd5_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libclang13-21.1.1-default_ha2db4b5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libdeflate-1.24-h76ddb4d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libexpat-2.7.1-hac47afa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libffi-3.4.6-h537db12_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libfreetype-2.14.1-h57928b3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libfreetype6-2.14.1-hdbac1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libgcc-15.1.0-h1383e82_5.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libglib-2.84.3-h1c1036b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libgomp-15.1.0-h1383e82_5.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libhwloc-2.12.1-default_h88281d1_1000.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libiconv-1.18-hc1393d2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libintl-0.22.5-h5728263_3.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libjpeg-turbo-3.1.0-h2466b09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/liblapack-3.9.0-35_hf9ab0e9_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/liblzma-5.8.1-h2466b09_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libpng-1.6.50-h7351971_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libsodium-1.0.20-hc70643c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libsqlite-3.50.4-hf5d6505_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libtiff-4.7.0-h550210a_6.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libwebp-base-1.6.0-h4d5522a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libwinpthread-12.0.0.r4.gg4f2fc60ca-h57928b3_9.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libxcb-1.17.0-h0e4246c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libxml2-2.13.8-h741aa76_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libxslt-1.1.43-h25c3957_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-1.3.1-h2466b09_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/llvm-openmp-20.1.8-hfa2b4ca_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/markupsafe-3.0.2-py311h5082efb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/matplotlib-3.10.6-py311h1ea47a8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/matplotlib-base-3.10.6-py311h1675fdf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.4-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/mkl-2024.2.2-h57928b3_16.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/nodejs-24.4.1-he453025_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-7.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/numpy-2.3.2-py311h80b3fa1_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/openjpeg-2.5.3-h24db6dd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/openssl-3.5.2-h725018a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pandas-2.3.2-py311h11fd7f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.5-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pcre2-10.45-h99c9b8b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pillow-11.3.0-py311h0f9b5fc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pixman-0.46.4-h5112557_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.4.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.52-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt_toolkit-3.0.52-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/psutil-7.0.0-py311h3485c13_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pthread-stubs-0.4-h0e40799_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.4-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pyside6-6.9.2-py311h5d1a980_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyh09c184e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/python-3.11.13-h3f84c4b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.2-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pywin32-311-py311hefeebc8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pywinpty-2.0.15-py311hda3d55a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pyyaml-6.0.2-py311h5082efb_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/pyzmq-27.1.0-py311hb77b9c8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/qhull-2020.2-hc790b64_5.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/qt6-main-6.9.2-h236c7cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/rpds-py-0.27.1-py311hf51aa87_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/scipy-1.16.2-py311h9a1c30b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh5737063_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/statsmodels-0.14.5-py311h17033d2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/tbb-2021.13.0-h18a62a1_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh5737063_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/tk-8.6.13-h2c6b04d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/tornado-6.5.2-py311h3485c13_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250822-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.26100.0-h57928b3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/unicodedata2-16.0.0-py311h3485c13_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vc-14.3-h41ae7f8_31.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.44.35208-h818238b_31.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vcomp14-14.44.35208-h818238b_31.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/win_inet_pton-1.1.0-pyh7428d3b_8.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/winpty-0.4.3-4.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/xorg-libxau-1.0.12-h0e40799_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/xorg-libxdmcp-1.1.5-h0e40799_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/yaml-0.2.5-h6a83c73_3.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/zeromq-4.3.5-h5bddc39_9.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/zstandard-0.25.0-py311hf893f09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/zstd-1.5.7-hbeecb71_2.conda
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/c9/d394706deb4c660137caf13e33d05a031d734eb99c051142e039d8ceb794/jiter-0.10.0-cp311-cp311-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fe/1b/25b7cccd4519c0b23c2dd636ad39d381abf113085ce4f7bec2b0dc755eb1/pydantic_core-2.33.2-cp311-cp311-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  purls: []
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23621
  timestamp: 1650670423406
- conda: https://conda.anaconda.org/conda-forge/win-64/_openmp_mutex-4.5-2_gnu.conda
  build_number: 8
  sha256: 1a62cd1f215fe0902e7004089693a78347a30ad687781dfda2289cab000e652d
  md5: 37e16618af5c4851a3f3d66dd0e11141
  depends:
  - libgomp >=7.5.0
  - libwinpthread >=12.0.0.r2.ggc561118da
  constrains:
  - openmp_impl 9999
  - msys2-conda-epoch <0.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 49468
  timestamp: 1718213032772
- conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
  sha256: b9214bc17e89bf2b691fad50d952b7f029f6148f4ac4fe7c60c08f093efdf745
  md5: 76df83c2a9035c54df5d04ff81bcc02d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  purls: []
  size: 566531
  timestamp: 1744668655747
- pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
  name: annotated-types
  version: 0.7.0
  sha256: 1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53
  requires_dist:
  - typing-extensions>=4.0.0 ; python_full_version < '3.9'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
  sha256: d1b50686672ebe7041e44811eda563e45b94a8354db67eca659040392ac74d63
  md5: cc2613bfa71dec0eb2113ee21ac9ccbf
  depends:
  - exceptiongroup >=1.0.2
  - idna >=2.8
  - python >=3.9
  - sniffio >=1.1
  - typing_extensions >=4.5
  - python
  constrains:
  - trio >=0.26.1
  - uvloop >=0.21
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/anyio?source=compressed-mapping
  size: 134857
  timestamp: 1754315087747
- conda: https://conda.anaconda.org/conda-forge/noarch/appnope-0.1.4-pyhd8ed1ab_1.conda
  sha256: 8f032b140ea4159806e4969a68b4a3c0a7cab1ad936eb958a2b5ffe5335e19bf
  md5: 54898d0f524c9dee622d44bbb081a8ab
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/appnope?source=hash-mapping
  size: 10076
  timestamp: 1733332433806
- conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
  sha256: bea62005badcb98b1ae1796ec5d70ea0fc9539e7d59708ac4e7d41e2f4bb0bad
  md5: 8ac12aff0860280ee0cff7fa2cf63f3b
  depends:
  - argon2-cffi-bindings
  - python >=3.9
  - typing-extensions
  constrains:
  - argon2_cffi ==999
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/argon2-cffi?source=hash-mapping
  size: 18715
  timestamp: 1749017288144
- conda: https://conda.anaconda.org/conda-forge/linux-64/argon2-cffi-bindings-25.1.0-py311h49ec1c0_0.conda
  sha256: d6d2f38ece253492a3e00800b5d4a5c2cc4b2de73b2c0fcc580c218f1cf58de6
  md5: 112c5e2b7fe99e3678bbd64316d38f0c
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.0.1
  - libgcc >=14
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/argon2-cffi-bindings?source=hash-mapping
  size: 35657
  timestamp: 1753994819264
- conda: https://conda.anaconda.org/conda-forge/osx-64/argon2-cffi-bindings-25.1.0-py311h13e5629_0.conda
  sha256: cab14d6bdcaf64f0911dcd994b51ddb753650d041a74c87a2107041763605e66
  md5: 8051f5bb22c95da482362f7a8c35fd68
  depends:
  - __osx >=10.13
  - cffi >=1.0.1
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/argon2-cffi-bindings?source=hash-mapping
  size: 33400
  timestamp: 1753995045262
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/argon2-cffi-bindings-25.1.0-py311h3696347_0.conda
  sha256: f5b4102716a568877e212a9d4c677027b887f215d4735acfe4532efb2da59de1
  md5: 3b4ba20f581ec2268df5a76c64232ae5
  depends:
  - __osx >=11.0
  - cffi >=1.0.1
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/argon2-cffi-bindings?source=hash-mapping
  size: 34325
  timestamp: 1753995031680
- conda: https://conda.anaconda.org/conda-forge/win-64/argon2-cffi-bindings-25.1.0-py311h3485c13_0.conda
  sha256: 4bde4487abbca4c8834a582928a80692a32ebba67e906ce676e931035a13d004
  md5: fdb37c9bd914e2a2c20f204f9cb15e6b
  depends:
  - cffi >=1.0.1
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/argon2-cffi-bindings?source=hash-mapping
  size: 38615
  timestamp: 1753995128176
- conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
  sha256: c4b0bdb3d5dee50b60db92f99da3e4c524d5240aafc0a5fcc15e45ae2d1a3cd1
  md5: 46b53236fdd990271b03c3978d4218a9
  depends:
  - python >=3.9
  - python-dateutil >=2.7.0
  - types-python-dateutil >=2.8.10
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/arrow?source=hash-mapping
  size: 99951
  timestamp: 1733584345583
- conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
  sha256: 93b14414b3b3ed91e286e1cbe4e7a60c4e1b1c730b0814d1e452a8ac4b9af593
  md5: 8f587de4bcf981e26228f268df374a9b
  depends:
  - python >=3.9
  constrains:
  - astroid >=2,<4
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/asttokens?source=hash-mapping
  size: 28206
  timestamp: 1733250564754
- conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
  sha256: 3b7233041e462d9eeb93ea1dfe7b18aca9c358832517072054bb8761df0c324b
  md5: d9d0f99095a9bb7e3641bca8c6ad2ac7
  depends:
  - python >=3.9
  - typing_extensions >=4.0.0
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/async-lru?source=hash-mapping
  size: 17335
  timestamp: 1742153708859
- conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
  sha256: 99c53ffbcb5dc58084faf18587b215f9ac8ced36bbfb55fa807c00967e419019
  md5: a10d11958cadc13fdb43df75f8b1903f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/attrs?source=hash-mapping
  size: 57181
  timestamp: 1741918625732
- conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
  sha256: 1c656a35800b7f57f7371605bc6507c8d3ad60fbaaec65876fce7f73df1fc8ac
  md5: 0a01c169f0ab0f91b26e77a3301fbfe4
  depends:
  - python >=3.9
  - pytz >=2015.7
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/babel?source=hash-mapping
  size: 6938256
  timestamp: 1738490268466
- conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.5-pyha770c72_0.conda
  sha256: d2124c0ea13527c7f54582269b3ae19541141a3740d6d779e7aa95aa82eaf561
  md5: de0fd9702fd4c1186e930b8c35af6b6b
  depends:
  - python >=3.10
  - soupsieve >=1.2
  - typing-extensions
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/beautifulsoup4?source=compressed-mapping
  size: 88278
  timestamp: 1756094375546
- conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
  sha256: a05971bb80cca50ce9977aad3f7fc053e54ea7d5321523efc7b9a6e12901d3cd
  md5: f0b4c8e370446ef89797608d60a564b3
  depends:
  - python >=3.9
  - webencodings
  - python
  constrains:
  - tinycss >=1.1.0,<1.5
  license: Apache-2.0 AND MIT
  purls:
  - pkg:pypi/bleach?source=hash-mapping
  size: 141405
  timestamp: 1737382993425
- conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
  sha256: 0aba699344275b3972bd751f9403316edea2ceb942db12f9f493b63c74774a46
  md5: a30e9406c873940383555af4c873220d
  depends:
  - bleach ==6.2.0 pyh29332c3_4
  - tinycss2
  license: Apache-2.0 AND MIT
  purls: []
  size: 4213
  timestamp: 1737382993425
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb03c661_4.conda
  sha256: 294526a54fa13635341729f250d0b1cf8f82cad1e6b83130304cbf3b6d8b74cc
  md5: eaf3fbd2aa97c212336de38a51fe404e
  depends:
  - __glibc >=2.17,<3.0.a0
  - brotli-bin 1.1.0 hb03c661_4
  - libbrotlidec 1.1.0 hb03c661_4
  - libbrotlienc 1.1.0 hb03c661_4
  - libgcc >=14
  license: MIT
  license_family: MIT
  purls: []
  size: 19883
  timestamp: 1756599394934
- conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-1.1.0-h1c43f85_4.conda
  sha256: 13847b7477bd66d0f718f337e7980c9a32f82ec4e4527c7e0a0983db2d798b8e
  md5: 1a0a37da4466d45c00fc818bb6b446b3
  depends:
  - __osx >=10.13
  - brotli-bin 1.1.0 h1c43f85_4
  - libbrotlidec 1.1.0 h1c43f85_4
  - libbrotlienc 1.1.0 h1c43f85_4
  license: MIT
  license_family: MIT
  purls: []
  size: 20022
  timestamp: 1756599872109
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-1.1.0-h6caf38d_4.conda
  sha256: 8aa8ee52b95fdc3ef09d476cbfa30df722809b16e6dca4a4f80e581012035b7b
  md5: ce8659623cea44cc812bc0bfae4041c5
  depends:
  - __osx >=11.0
  - brotli-bin 1.1.0 h6caf38d_4
  - libbrotlidec 1.1.0 h6caf38d_4
  - libbrotlienc 1.1.0 h6caf38d_4
  license: MIT
  license_family: MIT
  purls: []
  size: 20003
  timestamp: 1756599758165
- conda: https://conda.anaconda.org/conda-forge/win-64/brotli-1.1.0-hfd05255_4.conda
  sha256: df2a43cc4a99bd184cb249e62106dfa9f55b3d06df9b5fc67072b0336852ff65
  md5: 441706c019985cf109ced06458e6f742
  depends:
  - brotli-bin 1.1.0 hfd05255_4
  - libbrotlidec 1.1.0 hfd05255_4
  - libbrotlienc 1.1.0 hfd05255_4
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls: []
  size: 20233
  timestamp: 1756599828380
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb03c661_4.conda
  sha256: 444903c6e5c553175721a16b7c7de590ef754a15c28c99afbc8a963b35269517
  md5: ca4ed8015764937c81b830f7f5b68543
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlidec 1.1.0 hb03c661_4
  - libbrotlienc 1.1.0 hb03c661_4
  - libgcc >=14
  license: MIT
  license_family: MIT
  purls: []
  size: 19615
  timestamp: 1756599385418
- conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-bin-1.1.0-h1c43f85_4.conda
  sha256: 549ea0221019cfb4b370354f2c3ffbd4be1492740e1c73b2cdf9687ed6ad7364
  md5: 718fb8aa4c8cb953982416db9a82b349
  depends:
  - __osx >=10.13
  - libbrotlidec 1.1.0 h1c43f85_4
  - libbrotlienc 1.1.0 h1c43f85_4
  license: MIT
  license_family: MIT
  purls: []
  size: 17311
  timestamp: 1756599830763
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-bin-1.1.0-h6caf38d_4.conda
  sha256: e57d402b02c9287b7c02d9947d7b7b55a4f7d73341c210c233f6b388d4641e08
  md5: ab57f389f304c4d2eb86d8ae46d219c3
  depends:
  - __osx >=11.0
  - libbrotlidec 1.1.0 h6caf38d_4
  - libbrotlienc 1.1.0 h6caf38d_4
  license: MIT
  license_family: MIT
  purls: []
  size: 17373
  timestamp: 1756599741779
- conda: https://conda.anaconda.org/conda-forge/win-64/brotli-bin-1.1.0-hfd05255_4.conda
  sha256: e92c783502d95743b49b650c9276e9c56c7264da55429a5e45655150a6d1b0cf
  md5: ef022c8941d7dcc420c8533b0e419733
  depends:
  - libbrotlidec 1.1.0 hfd05255_4
  - libbrotlienc 1.1.0 hfd05255_4
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls: []
  size: 21425
  timestamp: 1756599802301
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py311h1ddb823_4.conda
  sha256: 318d4985acbf46457d254fbd6f0df80cc069890b5fc0013b3546d88eee1b1a1f
  md5: 7138a06a7b0d11a23cfae323e6010a08
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - libbrotlicommon 1.1.0 hb03c661_4
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/brotli?source=compressed-mapping
  size: 354304
  timestamp: 1756599521587
- conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-python-1.1.0-py311h7b20566_4.conda
  sha256: 10afc3a0df8e7447c56b0753848336eeeeea04be9bf1817569c45755392de14b
  md5: 13de3b969fd0ba12c4f6f9513f486f16
  depends:
  - __osx >=10.13
  - libcxx >=19
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - libbrotlicommon 1.1.0 h1c43f85_4
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/brotli?source=hash-mapping
  size: 368751
  timestamp: 1756600247737
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-python-1.1.0-py311hf719da1_4.conda
  sha256: 64645da991de052f0e522486cf97f8457fb37ed5c30d67655d3a32d2b9f56167
  md5: 4cd43bb7ba1a3cb4cd7a2e335f6d3c32
  depends:
  - __osx >=11.0
  - libcxx >=19
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  constrains:
  - libbrotlicommon 1.1.0 h6caf38d_4
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/brotli?source=hash-mapping
  size: 340889
  timestamp: 1756599941690
- conda: https://conda.anaconda.org/conda-forge/win-64/brotli-python-1.1.0-py311h3e6a449_4.conda
  sha256: d524edc172239fec70ad946e3b2fa1b9d7eea145ad80e9e66da25a4d815770ea
  md5: 21d3a7fa95d27938158009cd08e461f2
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - libbrotlicommon 1.1.0 hfd05255_4
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/brotli?source=hash-mapping
  size: 323289
  timestamp: 1756600106141
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/osx-64/bzip2-1.0.8-hfdf4475_7.conda
  sha256: cad153608b81fb24fc8c509357daa9ae4e49dfc535b2cb49b91e23dbd68fc3c5
  md5: 7ed4301d437b59045be7e051a0308211
  depends:
  - __osx >=10.13
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 134188
  timestamp: 1720974491916
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
  sha256: adfa71f158cbd872a36394c56c3568e6034aa55c623634b37a4836bd036e6b91
  md5: fc6948412dbbbe9a4c9ddbbcfe0a79ab
  depends:
  - __osx >=11.0
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 122909
  timestamp: 1720974522888
- conda: https://conda.anaconda.org/conda-forge/win-64/bzip2-1.0.8-h2466b09_7.conda
  sha256: 35a5dad92e88fdd7fc405e864ec239486f4f31eec229e31686e61a140a8e573b
  md5: 276e7ffe9ffe39688abc665ef0f45596
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 54927
  timestamp: 1720974860185
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-h4c7d964_0.conda
  sha256: 3b82f62baad3fd33827b01b0426e8203a2786c8f452f633740868296bcbe8485
  md5: c9e0c0f82f6e63323827db462b40ede8
  depends:
  - __win
  license: ISC
  purls: []
  size: 154489
  timestamp: 1754210967212
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
  sha256: 837b795a2bb39b75694ba910c13c15fa4998d4bb2a622c214a6a5174b2ae53d1
  md5: 74784ee3d225fc3dca89edb635b4e5cc
  depends:
  - __unix
  license: ISC
  purls: []
  size: 154402
  timestamp: 1754210968730
- conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
  noarch: python
  sha256: 561e6660f26c35d137ee150187d89767c988413c978e1b712d53f27ddf70ea17
  md5: 9b347a7ec10940d3f7941ff6c460b551
  depends:
  - cached_property >=1.5.2,<1.5.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 4134
  timestamp: 1615209571450
- conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
  sha256: 6dbf7a5070cc43d90a1e4c2ec0c541c69d8e30a0e25f50ce9f6e4a432e42c5d7
  md5: 576d629e47797577ab0f1b351297ef4a
  depends:
  - python >=3.6
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/cached-property?source=hash-mapping
  size: 11065
  timestamp: 1615209567874
- conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
  sha256: 3bd6a391ad60e471de76c0e9db34986c4b5058587fbf2efa5a7f54645e28c2c7
  md5: 09262e66b19567aff4f592fb53b28760
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  purls: []
  size: 978114
  timestamp: 1741554591855
- conda: https://conda.anaconda.org/conda-forge/win-64/cairo-1.18.4-h5782bbf_0.conda
  sha256: b9f577bddb033dba4533e851853924bfe7b7c1623d0697df382eef177308a917
  md5: 20e32ced54300292aff690a69c5e7b97
  depends:
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: LGPL-2.1-only or MPL-1.1
  purls: []
  size: 1524254
  timestamp: 1741555212198
- conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.8.3-pyhd8ed1ab_0.conda
  sha256: a1ad5b0a2a242f439608f22a538d2175cac4444b7b3f4e2b8c090ac337aaea40
  md5: 11f59985f49df4620890f3e746ed7102
  depends:
  - python >=3.9
  license: ISC
  purls:
  - pkg:pypi/certifi?source=compressed-mapping
  size: 158692
  timestamp: 1754231530168
- conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py311h5b438cf_1.conda
  sha256: bbd04c8729e6400fa358536b1007c1376cc396d569b71de10f1df7669d44170e
  md5: 82e0123a459d095ac99c76d150ccdacf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=14
  - pycparser
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cffi?source=compressed-mapping
  size: 303055
  timestamp: 1756808613387
- conda: https://conda.anaconda.org/conda-forge/osx-64/cffi-1.17.1-py311he66fa18_1.conda
  sha256: f5c6c73e0a389d2c89e10b36883e18cd3abd14756d9d01d53856aaae3131f219
  md5: 70cd671f73c5c08899d5c43366d37787
  depends:
  - __osx >=10.13
  - libffi >=3.4.6,<3.5.0a0
  - pycparser
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cffi?source=hash-mapping
  size: 294021
  timestamp: 1756808523082
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/cffi-1.17.1-py311h146a0b8_1.conda
  sha256: 97635f50d473eae17e11b954570efdc66b615dfa6321dd069742d6df4c14a8ba
  md5: 1c72ccc307e7681c34e1c06c1711ee33
  depends:
  - __osx >=11.0
  - libffi >=3.4.6,<3.5.0a0
  - pycparser
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cffi?source=compressed-mapping
  size: 293204
  timestamp: 1756808628759
- conda: https://conda.anaconda.org/conda-forge/win-64/cffi-1.17.1-py311h3485c13_1.conda
  sha256: 46baee342b50ce7fbf4c52267f73327cb0512b970332037c8911afee1e54f063
  md5: 553a1836df919ca232b80ce1324fa5bb
  depends:
  - pycparser
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cffi?source=hash-mapping
  size: 296743
  timestamp: 1756808544874
- conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.3-pyhd8ed1ab_0.conda
  sha256: 838d5a011f0e7422be6427becba3de743c78f3874ad2743c341accbba9bb2624
  md5: 7e7d5ef1b9ed630e4a1c358d6bc62284
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/charset-normalizer?source=hash-mapping
  size: 51033
  timestamp: 1754767444665
- conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
  sha256: ab29d57dc70786c1269633ba3dff20288b81664d3ff8d21af995742e2bb03287
  md5: 962b9857ee8e7018c22f2776ffa0b2d7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/colorama?source=hash-mapping
  size: 27011
  timestamp: 1733218222191
- conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
  sha256: 576a44729314ad9e4e5ebe055fbf48beb8116b60e58f9070278985b2b634f212
  md5: 2da13f2b299d8e1995bafbbe9689a2f7
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/comm?source=hash-mapping
  size: 14690
  timestamp: 1753453984907
- conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.3-py311hdf67eae_2.conda
  sha256: cb35e53fc4fc2ae59c85303b0668d05fa3be9cd9f8b27a127882f47aa795895b
  md5: bb6a0f88cf345f7e7a143d349dae6d9f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - numpy >=1.25
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/contourpy?source=hash-mapping
  size: 296784
  timestamp: 1756544804579
- conda: https://conda.anaconda.org/conda-forge/osx-64/contourpy-1.3.3-py311hd4d69bb_2.conda
  sha256: 69740b02124fd104b0c14494bc333cfc1bd64508d9dd0075a1493935866fbd64
  md5: 7d3b5e08077a62b8f24737e54aa3fe81
  depends:
  - __osx >=10.13
  - libcxx >=19
  - numpy >=1.25
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/contourpy?source=hash-mapping
  size: 269105
  timestamp: 1756545009456
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/contourpy-1.3.3-py311h57a9ea7_2.conda
  sha256: 3d85887270eb5f9f82025c93956cef6ff12a1aab49dbf7cba5ca4ee0544d4182
  md5: 66103afd2e02f1a416930dc352ae327b
  depends:
  - __osx >=11.0
  - libcxx >=19
  - numpy >=1.25
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/contourpy?source=hash-mapping
  size: 259587
  timestamp: 1756545016847
- conda: https://conda.anaconda.org/conda-forge/win-64/contourpy-1.3.3-py311h3fd045d_2.conda
  sha256: 620d21eedddae5c2f8edb8c549c46a7204356ceff6b2d6c5560e4b5ce59a757d
  md5: 327d9807b7aa0889a859070c550731d4
  depends:
  - numpy >=1.25
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/contourpy?source=hash-mapping
  size: 225470
  timestamp: 1756544991146
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.11.13-py311hd8ed1ab_0.conda
  noarch: generic
  sha256: ab70477f5cfb60961ba27d84a4c933a24705ac4b1736d8f3da14858e95bbfa7a
  md5: 4666fd336f6d48d866a58490684704cd
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi * *_cp311
  license: Python-2.0
  purls: []
  size: 47495
  timestamp: 1749048148121
- conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9827efa891e507a91a8a2acf64e210d2aff394e1cde432ad08e1f8c66b12293c
  md5: 44600c4667a319d67dbe0681fc0bc833
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/cycler?source=hash-mapping
  size: 13399
  timestamp: 1733332563512
- conda: https://conda.anaconda.org/conda-forge/linux-64/cyrus-sasl-2.1.28-hd9c7081_0.conda
  sha256: ee09ad7610c12c7008262d713416d0b58bf365bc38584dce48950025850bdf3f
  md5: cae723309a49399d2949362f4ab5c9e4
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libntlm >=1.8,<2.0a0
  - libstdcxx >=13
  - libxcrypt >=4.4.36
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause-Attribution
  license_family: BSD
  purls: []
  size: 209774
  timestamp: 1750239039316
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
  sha256: 3b988146a50e165f0fa4e839545c679af88e4782ec284cc7b6d07dd226d6a068
  md5: 679616eb5ad4e521c83da4650860aba7
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - libglib >=2.84.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 437860
  timestamp: 1747855126005
- conda: https://conda.anaconda.org/conda-forge/linux-64/debugpy-1.8.16-py311hc665b79_1.conda
  sha256: 19b0d1d9b0459db1466ad5846f6a30408ca9bbe244dcbbf32708116b564ceb11
  md5: 06e8c743932cc7788624128d08bc8806
  depends:
  - python
  - libgcc >=14
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/debugpy?source=hash-mapping
  size: 2729957
  timestamp: 1756742061937
- conda: https://conda.anaconda.org/conda-forge/osx-64/debugpy-1.8.16-py311hc651eee_1.conda
  sha256: 7f7300ee62b58658813e99a08f4a6f8daf585420cab6330f083ae697f569e66a
  md5: 32c16e5c0e427989aff77ead02bf7f88
  depends:
  - python
  - __osx >=10.13
  - libcxx >=19
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/debugpy?source=hash-mapping
  size: 2665552
  timestamp: 1756742018979
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/debugpy-1.8.16-py311ha59bd64_1.conda
  sha256: eea58c83203a96c1967574d495860bab109c52e10b41c5ac12daad7b66b81e70
  md5: 9d7f1641fc7385ed8dfc337d35ad4008
  depends:
  - python
  - libcxx >=19
  - python 3.11.* *_cpython
  - __osx >=11.0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/debugpy?source=hash-mapping
  size: 2666633
  timestamp: 1756742041729
- conda: https://conda.anaconda.org/conda-forge/win-64/debugpy-1.8.16-py311h5dfdfe8_1.conda
  sha256: 810fa69eca6adfbf707e2e31e26f24842ab313d2efbfdb8e73c15c164a8010d9
  md5: 5996fd469da1e196fd42c72a7b7a65ca
  depends:
  - python
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/debugpy?source=hash-mapping
  size: 3933261
  timestamp: 1756742011482
- conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
  sha256: c17c6b9937c08ad63cb20a26f403a3234088e57d4455600974a0ce865cb14017
  md5: 9ce473d1d1be1cc3810856a48b3fab32
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/decorator?source=hash-mapping
  size: 14129
  timestamp: 1740385067843
- conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
  sha256: 9717a059677553562a8f38ff07f3b9f61727bd614f505658b0a5ecbcf8df89be
  md5: 961b3a227b437d82ad7054484cfa71b2
  depends:
  - python >=3.6
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/defusedxml?source=hash-mapping
  size: 24062
  timestamp: 1615232388757
- pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
  name: distro
  version: 1.9.0
  sha256: 7bffd925d65168f85027d8da9af6bddab658135b840670a223589bc0c8ef02b2
  requires_python: '>=3.6'
- conda: https://conda.anaconda.org/conda-forge/linux-64/double-conversion-3.3.1-h5888daf_0.conda
  sha256: 1bcc132fbcc13f9ad69da7aa87f60ea41de7ed4d09f3a00ff6e0e70e1c690bc2
  md5: bfd56492d8346d669010eccafe0ba058
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 69544
  timestamp: 1739569648873
- conda: https://conda.anaconda.org/conda-forge/win-64/double-conversion-3.3.1-he0c23c2_0.conda
  sha256: b1fee32ef36a98159f0a2a96c4e734dfc9adff73acd444940831b22c1fb6d5c0
  md5: e9a1402439c18a4e3c7a52e4246e9e1c
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 71355
  timestamp: 1739570178995
- conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
  sha256: ce61f4f99401a4bd455b89909153b40b9c823276aefcbb06f2044618696009ca
  md5: 72e42d28960d875c7654614f8b50939a
  depends:
  - python >=3.9
  - typing_extensions >=4.6.0
  license: MIT and PSF-2.0
  purls:
  - pkg:pypi/exceptiongroup?source=hash-mapping
  size: 21284
  timestamp: 1746947398083
- conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.1-pyhd8ed1ab_0.conda
  sha256: 210c8165a58fdbf16e626aac93cc4c14dbd551a01d1516be5ecad795d2422cad
  md5: ff9efb7f7469aed3c4a8106ffa29593c
  depends:
  - python >=3.10
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/executing?source=compressed-mapping
  size: 30753
  timestamp: 1756729456476
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 397370
  timestamp: 1566932522327
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  purls: []
  size: 96530
  timestamp: 1620479909603
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  purls: []
  size: 700814
  timestamp: 1620479612257
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
  sha256: 2821ec1dc454bd8b9a31d0ed22a7ce22422c0aef163c59f49dfdf915d0f0ca14
  md5: 49023d73832ef61042f6a237cb2687e7
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  purls: []
  size: 1620504
  timestamp: 1727511233259
- conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
  sha256: 7093aa19d6df5ccb6ca50329ef8510c6acb6b0d8001191909397368b65b02113
  md5: 8f5b0b297b59e1ac160ad4beec99dbee
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 265599
  timestamp: 1730283881107
- conda: https://conda.anaconda.org/conda-forge/win-64/fontconfig-2.15.0-h765892d_1.conda
  sha256: ed122fc858fb95768ca9ca77e73c8d9ddc21d4b2e13aaab5281e27593e840691
  md5: 9bb0026a2131b09404c59c4290c697cd
  depends:
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libiconv >=1.17,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 192355
  timestamp: 1730284147944
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3667
  timestamp: 1566974674465
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 4102
  timestamp: 1566932280397
- conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.59.2-py311h3778330_0.conda
  sha256: f2685b212f3d84d2ba4fc89a03442724a94166ee8a9c1719efed0d7a07d474cb
  md5: 5be2463c4d16a021dd571d7bf56ac799
  depends:
  - __glibc >=2.17,<3.0.a0
  - brotli
  - libgcc >=14
  - munkres
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - unicodedata2 >=15.1.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/fonttools?source=hash-mapping
  size: 2934780
  timestamp: 1756328826529
- conda: https://conda.anaconda.org/conda-forge/osx-64/fonttools-4.59.2-py311hfbe4617_0.conda
  sha256: 84dddca09970ecd2fe32f557faa1b9706bfeea6433e415e797c3313c9a5300a9
  md5: d5648fdf64b8aa74dddd992740d6ccc5
  depends:
  - __osx >=10.13
  - brotli
  - munkres
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - unicodedata2 >=15.1.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/fonttools?source=hash-mapping
  size: 2841454
  timestamp: 1756328929077
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/fonttools-4.59.2-py311h2fe624c_0.conda
  sha256: 2d3dadff13a846513f81ece2ae356e4502f4edb5a5ebe58d21ecac7befed072a
  md5: 70bc71c4717c3a4700988f96eeabf09a
  depends:
  - __osx >=11.0
  - brotli
  - munkres
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  - unicodedata2 >=15.1.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/fonttools?source=hash-mapping
  size: 2866929
  timestamp: 1756328916569
- conda: https://conda.anaconda.org/conda-forge/win-64/fonttools-4.59.2-py311h3f79411_0.conda
  sha256: e835c0f2d9070a9262820e9cf5177324c7df2148c4d85d756f02b38e443bd9eb
  md5: 6c399663cab648a17883bf73f3057f04
  depends:
  - brotli
  - munkres
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - unicodedata2 >=15.1.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/fonttools?source=hash-mapping
  size: 2523927
  timestamp: 1756329034557
- conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
  sha256: 2509992ec2fd38ab27c7cdb42cf6cadc566a1cc0d1021a2673475d9fa87c6276
  md5: d3549fd50d450b6d9e7dddff25dd2110
  depends:
  - cached-property >=1.3.0
  - python >=3.9,<4
  license: MPL-2.0
  license_family: MOZILLA
  purls:
  - pkg:pypi/fqdn?source=hash-mapping
  size: 16705
  timestamp: 1733327494780
- conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.14.1-ha770c72_0.conda
  sha256: bf8e4dffe46f7d25dc06f31038cacb01672c47b9f45201f065b0f4d00ab0a83e
  md5: 4afc585cd97ba8a23809406cd8a9eda8
  depends:
  - libfreetype 2.14.1 ha770c72_0
  - libfreetype6 2.14.1 h73754d4_0
  license: GPL-2.0-only OR FTL
  purls: []
  size: 173114
  timestamp: 1757945422243
- conda: https://conda.anaconda.org/conda-forge/osx-64/freetype-2.14.1-h694c41f_0.conda
  sha256: 9f8282510db291496e89618fc66a58a1124fe7a6276fbd57ed18c602ce2576e9
  md5: ca641fdf8b7803f4b7212b6d66375930
  depends:
  - libfreetype 2.14.1 h694c41f_0
  - libfreetype6 2.14.1 h6912278_0
  license: GPL-2.0-only OR FTL
  purls: []
  size: 173969
  timestamp: 1757945973505
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/freetype-2.14.1-hce30654_0.conda
  sha256: 14427aecd72e973a73d5f9dfd0e40b6bc3791d253de09b7bf233f6a9a190fd17
  md5: 1ec9a1ee7a2c9339774ad9bb6fe6caec
  depends:
  - libfreetype 2.14.1 hce30654_0
  - libfreetype6 2.14.1 h6da58f4_0
  license: GPL-2.0-only OR FTL
  purls: []
  size: 173399
  timestamp: 1757947175403
- conda: https://conda.anaconda.org/conda-forge/win-64/freetype-2.14.1-h57928b3_0.conda
  sha256: a9b3313edea0bf14ea6147ea43a1059d0bf78771a1336d2c8282891efc57709a
  md5: d69c21967f35eb2ce7f1f85d6b6022d3
  depends:
  - libfreetype 2.14.1 h57928b3_0
  - libfreetype6 2.14.1 hdbac1cb_0
  license: GPL-2.0-only OR FTL
  purls: []
  size: 184553
  timestamp: 1757946164012
- conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-hecca717_2.conda
  sha256: 25ba37da5c39697a77fce2c9a15e48cf0a84f1464ad2aafbe53d8357a9f6cc8c
  md5: 2cd94587f3a401ae05e03a6caf09539d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 99596
  timestamp: 1755102025473
- conda: https://conda.anaconda.org/conda-forge/win-64/graphite2-1.3.14-hac47afa_2.conda
  sha256: 5f1714b07252f885a62521b625898326ade6ca25fbc20727cfe9a88f68a54bfd
  md5: b785694dd3ec77a011ccf0c24725382b
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 96336
  timestamp: 1755102441729
- conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
  sha256: f64b68148c478c3bfc8f8d519541de7d2616bf59d44485a5271041d40c061887
  md5: 4b69232755285701bc86a5afe4d9933a
  depends:
  - python >=3.9
  - typing_extensions
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/h11?source=hash-mapping
  size: 37697
  timestamp: 1745526482242
- conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.3.0-pyhcf101f3_0.conda
  sha256: 84c64443368f84b600bfecc529a1194a3b14c3656ee2e832d15a20e0329b6da3
  md5: 164fc43f0b53b6e3a7bc7dce5e4f1dc9
  depends:
  - python >=3.10
  - hyperframe >=6.1,<7
  - hpack >=4.1,<5
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/h2?source=compressed-mapping
  size: 95967
  timestamp: 1756364871835
- conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.4.5-h15599e2_0.conda
  sha256: 9d0d74858e8f8b76f6d3bf11a7390e6eb18eb743dd6e5fd7c4e9822634556f6d
  md5: 1276ae4aa3832a449fcb4253c30da4bc
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - graphite2 >=1.3.14,<2.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.1,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=14
  - libglib >=2.84.3,<3.0a0
  - libstdcxx >=14
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 2402438
  timestamp: 1756738217200
- conda: https://conda.anaconda.org/conda-forge/win-64/harfbuzz-11.4.5-h5f2951f_0.conda
  sha256: e1aaf8cf922cb7c7dabc12ddcad16c218b926c5e43d845288a4a8a0910df1b18
  md5: e9f9b4c46f6bc9b51adf57909b4d4652
  depends:
  - cairo >=1.18.4,<2.0a0
  - graphite2 >=1.3.14,<2.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.1,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libglib >=2.84.3,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 1134542
  timestamp: 1756738659278
- conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
  sha256: 6ad78a180576c706aabeb5b4c8ceb97c0cb25f1e112d76495bff23e3779948ba
  md5: 0a802cb9888dd14eeefc611f05c40b6e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/hpack?source=hash-mapping
  size: 30731
  timestamp: 1737618390337
- conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
  sha256: 04d49cb3c42714ce533a8553986e1642d0549a05dc5cc48e0d43ff5be6679a5b
  md5: 4f14640d58e2cc0aa0819d9d8ba125bb
  depends:
  - python >=3.9
  - h11 >=0.16
  - h2 >=3,<5
  - sniffio 1.*
  - anyio >=4.0,<5.0
  - certifi
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/httpcore?source=hash-mapping
  size: 49483
  timestamp: 1745602916758
- conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
  sha256: cd0f1de3697b252df95f98383e9edb1d00386bfdd03fdf607fa42fe5fcb09950
  md5: d6989ead454181f4f9bc987d3dc4e285
  depends:
  - anyio
  - certifi
  - httpcore 1.*
  - idna
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/httpx?source=hash-mapping
  size: 63082
  timestamp: 1733663449209
- conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
  sha256: 77af6f5fe8b62ca07d09ac60127a30d9069fdc3c68d6b256754d0ffb1f7779f8
  md5: 8e6923fc12f1fe8f8c4e5c9f343256ac
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/hyperframe?source=hash-mapping
  size: 17397
  timestamp: 1737618427549
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  purls: []
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/osx-64/icu-75.1-h120a0e1_0.conda
  sha256: 2e64307532f482a0929412976c8450c719d558ba20c0962832132fd0d07ba7a7
  md5: d68d48a3060eb5abdc1cdc8e2a3a5966
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 11761697
  timestamp: 1720853679409
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
  sha256: 9ba12c93406f3df5ab0a43db8a4b4ef67a5871dfd401010fbe29b218b2cbe620
  md5: 5eb22c1d7b3fc4abb50d92d621583137
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 11857802
  timestamp: 1720853997952
- conda: https://conda.anaconda.org/conda-forge/win-64/icu-75.1-he0c23c2_0.conda
  sha256: 1d04369a1860a1e9e371b9fc82dd0092b616adcf057d6c88371856669280e920
  md5: 8579b6bb8d18be7c0b27fb08adeeeb40
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 14544252
  timestamp: 1720853966338
- conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
  sha256: d7a472c9fd479e2e8dcb83fb8d433fce971ea369d704ece380e876f9c3494e87
  md5: 39a4f67be3286c86d696df570b1201b7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/idna?source=hash-mapping
  size: 49765
  timestamp: 1733211921194
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
  sha256: c18ab120a0613ada4391b15981d86ff777b5690ca461ea7e9e49531e8f374745
  md5: 63ccfdc3a3ce25b027b8767eb722fca8
  depends:
  - python >=3.9
  - zipp >=3.20
  - python
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/importlib-metadata?source=hash-mapping
  size: 34641
  timestamp: 1747934053147
- conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh3521513_0.conda
  sha256: 3dd6fcdde5e40a3088c9ecd72c29c6e5b1429b99e927f41c8cee944a07062046
  md5: 953007d45edeb098522ac860aade4fcf
  depends:
  - __win
  - comm >=0.1.1
  - debugpy >=1.6.5
  - ipython >=7.23.1
  - jupyter_client >=8.0.0
  - jupyter_core >=4.12,!=5.0.*
  - matplotlib-inline >=0.1
  - nest-asyncio >=1.4
  - packaging >=22
  - psutil >=5.7
  - python >=3.9
  - pyzmq >=25
  - tornado >=6.2
  - traitlets >=5.4.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipykernel?source=hash-mapping
  size: 121976
  timestamp: 1754353094360
- conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh82676e8_0.conda
  sha256: cfc2c4e31dfedbb3d124d0055f55fda4694538fb790d52cd1b37af5312833e36
  md5: b0cc25825ce9212b8bee37829abad4d6
  depends:
  - __linux
  - comm >=0.1.1
  - debugpy >=1.6.5
  - ipython >=7.23.1
  - jupyter_client >=8.0.0
  - jupyter_core >=4.12,!=5.0.*
  - matplotlib-inline >=0.1
  - nest-asyncio >=1.4
  - packaging >=22
  - psutil >=5.7
  - python >=3.9
  - pyzmq >=25
  - tornado >=6.2
  - traitlets >=5.4.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipykernel?source=hash-mapping
  size: 121367
  timestamp: 1754352984703
- conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh92f572d_0.conda
  sha256: ec80ed5f68c96dd46ff1b533b28d2094b6f07e2ec8115c8c60803920fdd6eb13
  md5: f208c1a85786e617a91329fa5201168c
  depends:
  - __osx
  - appnope
  - comm >=0.1.1
  - debugpy >=1.6.5
  - ipython >=7.23.1
  - jupyter_client >=8.0.0
  - jupyter_core >=4.12,!=5.0.*
  - matplotlib-inline >=0.1
  - nest-asyncio >=1.4
  - packaging >=22
  - psutil >=5.7
  - python >=3.9
  - pyzmq >=25
  - tornado >=6.2
  - traitlets >=5.4.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipykernel?source=hash-mapping
  size: 121397
  timestamp: 1754353050327
- conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.5.0-pyh6be1c34_0.conda
  sha256: 658c547dafb10cd0989f2cdf72f8ee9fe8f66240307b64555ee43f6908e9d0ad
  md5: aec1868dd4cbe028b2c8cb11377895a6
  depends:
  - __win
  - colorama
  - decorator
  - exceptiongroup
  - ipython_pygments_lexers
  - jedi >=0.16
  - matplotlib-inline
  - pickleshare
  - prompt-toolkit >=3.0.41,<3.1.0
  - pygments >=2.4.0
  - python >=3.11
  - stack_data
  - traitlets >=5.13.0
  - typing_extensions >=4.6
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipython?source=hash-mapping
  size: 630157
  timestamp: 1756474536497
- conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.5.0-pyhfa0c392_0.conda
  sha256: e9ca009d3aab9d8a85f0241d6ada2c7fbc84072008e95f803fa59da3294aa863
  md5: c0916cc4b733577cd41df93884d857b0
  depends:
  - __unix
  - pexpect >4.3
  - decorator
  - exceptiongroup
  - ipython_pygments_lexers
  - jedi >=0.16
  - matplotlib-inline
  - pickleshare
  - prompt-toolkit >=3.0.41,<3.1.0
  - pygments >=2.4.0
  - python >=3.11
  - stack_data
  - traitlets >=5.13.0
  - typing_extensions >=4.6
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipython?source=hash-mapping
  size: 630826
  timestamp: 1756474504536
- conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
  sha256: 894682a42a7d659ae12878dbcb274516a7031bbea9104e92f8e88c1f2765a104
  md5: bd80ba060603cc228d9d81c257093119
  depends:
  - pygments
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipython-pygments-lexers?source=hash-mapping
  size: 13993
  timestamp: 1737123723464
- conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
  sha256: fd496e7d48403246f534c5eec09fc1e63ac7beb1fa06541d6ba71f56b30cf29b
  md5: 7c9449eac5975ef2d7753da262a72707
  depends:
  - comm >=0.1.3
  - ipython >=6.1.0
  - jupyterlab_widgets >=3.0.15,<3.1.0
  - python >=3.9
  - traitlets >=4.3.1
  - widgetsnbextension >=4.0.14,<4.1.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipywidgets?source=hash-mapping
  size: 114557
  timestamp: 1746454722402
- conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
  sha256: 08e838d29c134a7684bca0468401d26840f41c92267c4126d7b43a6b533b0aed
  md5: 0b0154421989637d424ccf0f104be51a
  depends:
  - arrow >=0.15.0
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/isoduration?source=hash-mapping
  size: 19832
  timestamp: 1733493720346
- conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
  sha256: 92c4d217e2dc68983f724aa983cca5464dcb929c566627b26a2511159667dba8
  md5: a4f4c5dc9b80bc50e0d3dc4e6e8f1bd9
  depends:
  - parso >=0.8.3,<0.9.0
  - python >=3.9
  license: Apache-2.0 AND MIT
  purls:
  - pkg:pypi/jedi?source=hash-mapping
  size: 843646
  timestamp: 1733300981994
- conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
  sha256: f1ac18b11637ddadc05642e8185a851c7fab5998c6f5470d716812fae943b2af
  md5: 446bd6c8cb26050d528881df495ce646
  depends:
  - markupsafe >=2.0
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jinja2?source=hash-mapping
  size: 112714
  timestamp: 1741263433881
- pypi: https://files.pythonhosted.org/packages/1b/dd/6cefc6bd68b1c3c979cecfa7029ab582b57690a31cd2f346c4d0ce7951b6/jiter-0.10.0-cp311-cp311-macosx_10_12_x86_64.whl
  name: jiter
  version: 0.10.0
  sha256: 3bebe0c558e19902c96e99217e0b8e8b17d570906e72ed8a87170bc290b1e978
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/81/5a/0e73541b6edd3f4aada586c24e50626c7815c561a7ba337d6a7eb0a915b4/jiter-0.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: jiter
  version: 0.10.0
  sha256: 4c440ea003ad10927a30521a9062ce10b5479592e8a70da27f21eeb457b4a9c5
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/be/cf/fc33f5159ce132be1d8dd57251a1ec7a631c7df4bd11e1cd198308c6ae32/jiter-0.10.0-cp311-cp311-macosx_11_0_arm64.whl
  name: jiter
  version: 0.10.0
  sha256: 558cc7e44fd8e507a236bee6a02fa17199ba752874400a0ca6cd6e2196cdb7dc
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/c2/c9/d394706deb4c660137caf13e33d05a031d734eb99c051142e039d8ceb794/jiter-0.10.0-cp311-cp311-win_amd64.whl
  name: jiter
  version: 0.10.0
  sha256: 9c9c1d5f10e18909e993f9641f12fe1c77b3e9b533ee94ffa970acc14ded3812
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.1-pyhd8ed1ab_0.conda
  sha256: 4e08ccf9fa1103b617a4167a270768de736a36be795c6cd34c2761100d332f74
  md5: 0fc93f473c31a2f85c0bde213e7c63ca
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/json5?source=hash-mapping
  size: 34191
  timestamp: 1755034963991
- conda: https://conda.anaconda.org/conda-forge/linux-64/jsonpointer-3.0.0-py311h38be061_2.conda
  sha256: 4e744b30e3002b519c48868b3f5671328274d1d78cc8cbc0cda43057b570c508
  md5: 5dd29601defbcc14ac6953d9504a80a7
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jsonpointer?source=hash-mapping
  size: 18368
  timestamp: 1756754243123
- conda: https://conda.anaconda.org/conda-forge/osx-64/jsonpointer-3.0.0-py311h6eed73b_2.conda
  sha256: 8e0f5af4d5bd59f52d27926750416638e32a65d58a202593fa0e97f312ad78c3
  md5: 9983b3959da3b695c8da48c93510cbdf
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jsonpointer?source=hash-mapping
  size: 18407
  timestamp: 1756754411612
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/jsonpointer-3.0.0-py311h267d04e_2.conda
  sha256: 92b998fa9e68b7793b5b15882932f7703cdc1cc6e1348d0a1799567ef6f04428
  md5: 0edc5f25c32d86d5b4327e0f4de539f9
  depends:
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jsonpointer?source=hash-mapping
  size: 18716
  timestamp: 1756754690458
- conda: https://conda.anaconda.org/conda-forge/win-64/jsonpointer-3.0.0-py311h1ea47a8_2.conda
  sha256: 64bcf78dbbda7ec523672c4b3f085527fd109732518e33907eac6b8049125113
  md5: c8f80d7bee5c66371969936eba774c45
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jsonpointer?source=hash-mapping
  size: 43432
  timestamp: 1756754355044
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.1-pyhe01879c_0.conda
  sha256: ac377ef7762e49cb9c4f985f1281eeff471e9adc3402526eea78e6ac6589cf1d
  md5: 341fd940c242cf33e832c0402face56f
  depends:
  - attrs >=22.2.0
  - jsonschema-specifications >=2023.3.6
  - python >=3.9
  - referencing >=0.28.4
  - rpds-py >=0.7.1
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/jsonschema?source=compressed-mapping
  size: 81688
  timestamp: 1755595646123
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.9.1-pyhcf101f3_0.conda
  sha256: 0a4f3b132f0faca10c89fdf3b60e15abb62ded6fa80aebfc007d05965192aa04
  md5: 439cd0f567d697b20a8f45cb70a1005a
  depends:
  - python >=3.10
  - referencing >=0.31.0
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/jsonschema-specifications?source=compressed-mapping
  size: 19236
  timestamp: 1757335715225
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.1-he01879c_0.conda
  sha256: aef6705fe1335e6472e1b6365fcdb586356b18dceff72d8d6a315fc90e900ccf
  md5: 13e31c573c884962318a738405ca3487
  depends:
  - jsonschema >=4.25.1,<4.25.2.0a0
  - fqdn
  - idna
  - isoduration
  - jsonpointer >1.13
  - rfc3339-validator
  - rfc3986-validator >0.1.0
  - rfc3987-syntax >=1.1.0
  - uri-template
  - webcolors >=24.6.0
  license: MIT
  license_family: MIT
  purls: []
  size: 4744
  timestamp: 1755595646123
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-1.1.1-pyhd8ed1ab_1.conda
  sha256: b538e15067d05768d1c0532a6d9b0625922a1cce751dd6a2af04f7233a1a70e9
  md5: 9453512288d20847de4356327d0e1282
  depends:
  - ipykernel
  - ipywidgets
  - jupyter_console
  - jupyterlab
  - nbconvert-core
  - notebook
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter?source=hash-mapping
  size: 8891
  timestamp: 1733818677113
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.3.0-pyhcf101f3_0.conda
  sha256: 897ad2e2c2335ef3c2826d7805e16002a1fd0d509b4ae0bc66617f0e0ff07bc2
  md5: 62b7c96c6cd77f8173cc5cada6a9acaa
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_server >=1.1.2
  - python >=3.10
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-lsp?source=hash-mapping
  size: 60377
  timestamp: 1756388269267
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
  sha256: 19d8bd5bb2fde910ec59e081eeb59529491995ce0d653a5209366611023a0b3a
  md5: 4ebae00eae9705b0c3d6d1018a81d047
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-dateutil >=2.8.2
  - pyzmq >=23.0
  - tornado >=6.2
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-client?source=hash-mapping
  size: 106342
  timestamp: 1733441040958
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_console-6.6.3-pyhd8ed1ab_1.conda
  sha256: aee0cdd0cb2b9321d28450aec4e0fd43566efcd79e862d70ce49a68bf0539bcd
  md5: 801dbf535ec26508fac6d4b24adfb76e
  depends:
  - ipykernel >=6.14
  - ipython
  - jupyter_client >=7.0.0
  - jupyter_core >=4.12,!=5.0.*
  - prompt_toolkit >=3.0.30
  - pygments
  - python >=3.9
  - pyzmq >=17
  - traitlets >=5.4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-console?source=hash-mapping
  size: 26874
  timestamp: 1733818130068
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
  sha256: 56a7a7e907f15cca8c4f9b0c99488276d4cb10821d2d15df9245662184872e81
  md5: b7d89d860ebcda28a5303526cdee68ab
  depends:
  - __unix
  - platformdirs >=2.5
  - python >=3.8
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-core?source=hash-mapping
  size: 59562
  timestamp: 1748333186063
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh5737063_0.conda
  sha256: 928c2514c2974fda78447903217f01ca89a77eefedd46bf6a2fe97072df57e8d
  md5: 324e60a0d3f39f268e899709575ea3cd
  depends:
  - __win
  - cpython
  - platformdirs >=2.5
  - python >=3.8
  - pywin32 >=300
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-core?source=hash-mapping
  size: 59972
  timestamp: 1748333368923
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
  sha256: 37e6ac3ccf7afcc730c3b93cb91a13b9ae827fd306f35dd28f958a74a14878b5
  md5: f56000b36f09ab7533877e695e4e8cb0
  depends:
  - jsonschema-with-format-nongpl >=4.18.0
  - packaging
  - python >=3.9
  - python-json-logger >=2.0.4
  - pyyaml >=5.3
  - referencing
  - rfc3339-validator
  - rfc3986-validator >=0.1.1
  - traitlets >=5.3
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-events?source=hash-mapping
  size: 23647
  timestamp: 1738765986736
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.17.0-pyhcf101f3_0.conda
  sha256: 74c4e642be97c538dae1895f7052599dfd740d8bd251f727bce6453ce8d6cd9a
  md5: d79a87dcfa726bcea8e61275feed6f83
  depends:
  - anyio >=3.1.0
  - argon2-cffi >=21.1
  - jinja2 >=3.0.3
  - jupyter_client >=7.4.4
  - jupyter_core >=4.12,!=5.0.*
  - jupyter_events >=0.11.0
  - jupyter_server_terminals >=0.4.4
  - nbconvert-core >=6.4.4
  - nbformat >=5.3.0
  - overrides >=5.0
  - packaging >=22.0
  - prometheus_client >=0.9
  - python >=3.10
  - pyzmq >=24
  - send2trash >=1.8.2
  - terminado >=0.8.3
  - tornado >=6.2.0
  - traitlets >=5.6.0
  - websocket-client >=1.7
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-server?source=hash-mapping
  size: 347094
  timestamp: 1755870522134
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
  sha256: 0890fc79422191bc29edf17d7b42cff44ba254aa225d31eb30819f8772b775b8
  md5: 2d983ff1b82a1ccb6f2e9d8784bdd6bd
  depends:
  - python >=3.9
  - terminado >=0.8.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-server-terminals?source=hash-mapping
  size: 19711
  timestamp: 1733428049134
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.7-pyhd8ed1ab_0.conda
  sha256: 042bdb981ad5394530bee8329a10c76b9e17c12651d15a885d68e2cbbfef6869
  md5: 460d51bb21b7a4c4b6e100c824405fbb
  depends:
  - async-lru >=1.0.0
  - httpx >=0.25.0,<1
  - importlib-metadata >=4.8.3
  - ipykernel >=6.5.0,!=6.30.0
  - jinja2 >=3.0.3
  - jupyter-lsp >=2.0.0
  - jupyter_core
  - jupyter_server >=2.4.0,<3
  - jupyterlab_server >=2.27.1,<3
  - notebook-shim >=0.2
  - packaging
  - python >=3.10
  - setuptools >=41.1.0
  - tomli >=1.2.2
  - tornado >=6.2.0
  - traitlets
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyterlab?source=compressed-mapping
  size: 8479512
  timestamp: 1756911706349
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
  sha256: dc24b900742fdaf1e077d9a3458fd865711de80bca95fe3c6d46610c532c6ef0
  md5: fd312693df06da3578383232528c468d
  depends:
  - pygments >=2.4.1,<3
  - python >=3.9
  constrains:
  - jupyterlab >=4.0.8,<5.0.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyterlab-pygments?source=hash-mapping
  size: 18711
  timestamp: 1733328194037
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
  sha256: d03d0b7e23fa56d322993bc9786b3a43b88ccc26e58b77c756619a921ab30e86
  md5: 9dc4b2b0f41f0de41d27f3293e319357
  depends:
  - babel >=2.10
  - importlib-metadata >=4.8.3
  - jinja2 >=3.0.3
  - json5 >=0.9.0
  - jsonschema >=4.18
  - jupyter_server >=1.21,<3
  - packaging >=21.3
  - python >=3.9
  - requests >=2.31
  constrains:
  - openapi-core >=0.18.0,<0.19.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyterlab-server?source=hash-mapping
  size: 49449
  timestamp: 1733599666357
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
  sha256: 6214d345861b106076e7cb38b59761b24cd340c09e3f787e4e4992036ca3cd7e
  md5: ad100d215fad890ab0ee10418f36876f
  depends:
  - python >=3.9
  constrains:
  - jupyterlab >=3,<5
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyterlab-widgets?source=hash-mapping
  size: 189133
  timestamp: 1746450926999
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.3-hb9d3cd8_0.conda
  sha256: 0960d06048a7185d3542d850986d807c6e37ca2e644342dd0c72feefcf26c2a4
  md5: b38117a3c920364aff79f870c984b4a3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 134088
  timestamp: 1754905959823
- conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.9-py311h724c32c_1.conda
  sha256: 029a00a337e307256beab9cbaefc2c23cd28f040fff6f087703a63bc7487fc14
  md5: 92720706b174926bc7238cc24f3b5956
  depends:
  - python
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/kiwisolver?source=hash-mapping
  size: 78167
  timestamp: 1756467524636
- conda: https://conda.anaconda.org/conda-forge/osx-64/kiwisolver-1.4.9-py311ha94bed4_1.conda
  sha256: 58391bf10f4450020c792b2dbd4f1b0aa0281dd008c121b36a9eae63a9d4322b
  md5: a5e96ec3e24dd2fd9887ad284ee1a8c5
  depends:
  - python
  - __osx >=10.13
  - libcxx >=19
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/kiwisolver?source=hash-mapping
  size: 67533
  timestamp: 1756467598070
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/kiwisolver-1.4.9-py311h63e5c0c_1.conda
  sha256: ad672ae48e23e59a6adbff2b51fb47bfc1bf430f8e991dbf086b2506caf9eb31
  md5: d5778729cfb3846a9d329c8740f13420
  depends:
  - python
  - python 3.11.* *_cpython
  - libcxx >=19
  - __osx >=11.0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/kiwisolver?source=hash-mapping
  size: 66093
  timestamp: 1756467632843
- conda: https://conda.anaconda.org/conda-forge/win-64/kiwisolver-1.4.9-py311h275cad7_1.conda
  sha256: e5e759b61a71e16ba4637c9b08bb8e5c01ee678a47f3e980a7cacb8b0bee58b8
  md5: 62b8b3f148d7f47db02304a7de177d13
  depends:
  - python
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/kiwisolver?source=hash-mapping
  size: 73589
  timestamp: 1756467536839
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/osx-64/krb5-1.21.3-h37d8d59_0.conda
  sha256: 83b52685a4ce542772f0892a0f05764ac69d57187975579a0835ff255ae3ef9c
  md5: d4765c524b1d91567886bde656fb514b
  depends:
  - __osx >=10.13
  - libcxx >=16
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1185323
  timestamp: 1719463492984
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
  sha256: 4442f957c3c77d69d9da3521268cad5d54c9033f1a73f99cde0a3658937b159b
  md5: c6dc8a0fdec13a0565936655c33069a1
  depends:
  - __osx >=11.0
  - libcxx >=16
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1155530
  timestamp: 1719463474401
- conda: https://conda.anaconda.org/conda-forge/win-64/krb5-1.21.3-hdf4eb48_0.conda
  sha256: 18e8b3430d7d232dad132f574268f56b3eb1a19431d6d5de8c53c29e6c18fa81
  md5: 31aec030344e962fbd7dbbbbd68e60a9
  depends:
  - openssl >=3.3.1,<4.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 712034
  timestamp: 1719463874284
- conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
  sha256: 637a9c32e15a4333f1f9c91e0a506dbab4a6dab7ee83e126951159c916c81c99
  md5: 3a8063b25e603999188ed4bbf3485404
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/lark?source=hash-mapping
  size: 92093
  timestamp: 1734709450256
- conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
  sha256: d6a61830a354da022eae93fa896d0991385a875c6bba53c82263a289deda9db8
  md5: 000e85703f0fd9594c81710dd5066471
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 248046
  timestamp: 1739160907615
- conda: https://conda.anaconda.org/conda-forge/osx-64/lcms2-2.17-h72f5680_0.conda
  sha256: bcb81543e49ff23e18dea79ef322ab44b8189fb11141b1af99d058503233a5fc
  md5: bf210d0c63f2afb9e414a858b79f0eaa
  depends:
  - __osx >=10.13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 226001
  timestamp: 1739161050843
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/lcms2-2.17-h7eeda09_0.conda
  sha256: 310a62c2f074ebd5aa43b3cd4b00d46385ce680fa2132ecee255a200e2d2f15f
  md5: 92a61fd30b19ebd5c1621a5bfe6d8b5f
  depends:
  - __osx >=11.0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 212125
  timestamp: 1739161108467
- conda: https://conda.anaconda.org/conda-forge/win-64/lcms2-2.17-hbcf6048_0.conda
  sha256: 7712eab5f1a35ca3ea6db48ead49e0d6ac7f96f8560da8023e61b3dbe4f3b25d
  md5: 3538827f77b82a837fa681a4579e37a1
  depends:
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 510641
  timestamp: 1739161381270
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
  sha256: 1a620f27d79217c1295049ba214c2f80372062fd251b569e9873d4a953d27554
  md5: 0be7c6e070c19105f966d3758448d018
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.44
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 676044
  timestamp: 1752032747103
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/osx-64/lerc-4.0.0-hcca01a6_1.conda
  sha256: cc1f1d7c30aa29da4474ec84026ec1032a8df1d7ec93f4af3b98bb793d01184e
  md5: 21f765ced1a0ef4070df53cb425e1967
  depends:
  - __osx >=10.13
  - libcxx >=18
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 248882
  timestamp: 1745264331196
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/lerc-4.0.0-hd64df32_1.conda
  sha256: 12361697f8ffc9968907d1a7b5830e34c670e4a59b638117a2cdfed8f63a38f8
  md5: a74332d9b60b62905e3d30709df08bf1
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 188306
  timestamp: 1745264362794
- conda: https://conda.anaconda.org/conda-forge/win-64/lerc-4.0.0-h6470a55_1.conda
  sha256: 868a3dff758cc676fa1286d3f36c3e0101cca56730f7be531ab84dc91ec58e9d
  md5: c1b81da6d29a14b542da14a36c9fbf3f
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 164701
  timestamp: 1745264384716
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-35_h59b9bed_openblas.conda
  build_number: 35
  sha256: 83d0755acd486660532003bc2562223504b57732bc7e250985391ce335692cf7
  md5: eaf80af526daf5745295d9964c2bd3cf
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - blas 2.135   openblas
  - liblapack  3.9.0   35*_openblas
  - libcblas   3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  - mkl <2025
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 16937
  timestamp: 1757002815691
- conda: https://conda.anaconda.org/conda-forge/osx-64/libblas-3.9.0-35_h7f60823_openblas.conda
  build_number: 35
  sha256: 29db26b643bd1e73d5c658bdd0d21ab3c1caa7cfcc62d1d368b1c8e25105e1a0
  md5: 4479d770836307d0bac28281d00ccfa9
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - liblapack  3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  - libcblas   3.9.0   35*_openblas
  - blas 2.135   openblas
  - mkl <2025
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17151
  timestamp: 1757003585301
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-35_h10e41b3_openblas.conda
  build_number: 35
  sha256: 52ddab13634559d8fc9c7808c52200613bb3825c6e0708820f5744aa55324702
  md5: 0b59bf8bb0bc16b2c5bdae947a44e1f1
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - libcblas   3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  - liblapack  3.9.0   35*_openblas
  - mkl <2025
  - blas 2.135   openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17191
  timestamp: 1757003619794
- conda: https://conda.anaconda.org/conda-forge/win-64/libblas-3.9.0-35_h5709861_mkl.conda
  build_number: 35
  sha256: 4180e7ab27ed03ddf01d7e599002fcba1b32dcb68214ee25da823bac371ed362
  md5: 45d98af023f8b4a7640b1f713ce6b602
  depends:
  - mkl >=2024.2.2,<2025.0a0
  constrains:
  - blas 2.135   mkl
  - liblapack  3.9.0   35*_mkl
  - libcblas   3.9.0   35*_mkl
  - liblapacke 3.9.0   35*_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 66044
  timestamp: 1757003486248
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb03c661_4.conda
  sha256: 2338a92d1de71f10c8cf70f7bb9775b0144a306d75c4812276749f54925612b6
  md5: 1d29d2e33fe59954af82ef54a8af3fe1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  license: MIT
  license_family: MIT
  purls: []
  size: 69333
  timestamp: 1756599354727
- conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlicommon-1.1.0-h1c43f85_4.conda
  sha256: 28c1a5f7dbe68342b7341d9584961216bd16f81aa3c7f1af317680213c00b46a
  md5: b8e1ee78815e0ba7835de4183304f96b
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 67948
  timestamp: 1756599727911
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlicommon-1.1.0-h6caf38d_4.conda
  sha256: 023b609ecc35bfee7935d65fcc5aba1a3ba6807cbba144a0730198c0914f7c79
  md5: 231cffe69d41716afe4525c5c1cc5ddd
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 68938
  timestamp: 1756599687687
- conda: https://conda.anaconda.org/conda-forge/win-64/libbrotlicommon-1.1.0-hfd05255_4.conda
  sha256: 65d0aaf1176761291987f37c8481be132060cc3dbe44b1550797bc27d1a0c920
  md5: 58aec7a295039d8614175eae3a4f8778
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls: []
  size: 71243
  timestamp: 1756599708777
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb03c661_4.conda
  sha256: fcec0d26f67741b122f0d5eff32f0393d7ebd3ee6bb866ae2f17f3425a850936
  md5: 5cb5a1c9a94a78f5b23684bcb845338d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb03c661_4
  - libgcc >=14
  license: MIT
  license_family: MIT
  purls: []
  size: 33406
  timestamp: 1756599364386
- conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlidec-1.1.0-h1c43f85_4.conda
  sha256: a287470602e8380c0bdb5e7a45ba3facac644432d7857f27b39d6ceb0dcbf8e9
  md5: 9cc4be0cc163d793d5d4bcc405c81bf3
  depends:
  - __osx >=10.13
  - libbrotlicommon 1.1.0 h1c43f85_4
  license: MIT
  license_family: MIT
  purls: []
  size: 30743
  timestamp: 1756599755474
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlidec-1.1.0-h6caf38d_4.conda
  sha256: 7f1cf83a00a494185fc087b00c355674a0f12e924b1b500d2c20519e98fdc064
  md5: cb7e7fe96c9eee23a464afd57648d2cd
  depends:
  - __osx >=11.0
  - libbrotlicommon 1.1.0 h6caf38d_4
  license: MIT
  license_family: MIT
  purls: []
  size: 29015
  timestamp: 1756599708339
- conda: https://conda.anaconda.org/conda-forge/win-64/libbrotlidec-1.1.0-hfd05255_4.conda
  sha256: aa03aff197ed503e38145d0d0f17c30382ac1c6d697535db24c98c272ef57194
  md5: bf0ced5177fec8c18a7b51d568590b7c
  depends:
  - libbrotlicommon 1.1.0 hfd05255_4
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls: []
  size: 33430
  timestamp: 1756599740173
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb03c661_4.conda
  sha256: d42c7f0afce21d5279a0d54ee9e64a2279d35a07a90e0c9545caae57d6d7dc57
  md5: 2e55011fa483edb8bfe3fd92e860cd79
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb03c661_4
  - libgcc >=14
  license: MIT
  license_family: MIT
  purls: []
  size: 289680
  timestamp: 1756599375485
- conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlienc-1.1.0-h1c43f85_4.conda
  sha256: 820caf0a78770758830adbab97fe300104981a5327683830d162b37bc23399e9
  md5: f2c000dc0185561b15de7f969f435e61
  depends:
  - __osx >=10.13
  - libbrotlicommon 1.1.0 h1c43f85_4
  license: MIT
  license_family: MIT
  purls: []
  size: 294904
  timestamp: 1756599789206
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlienc-1.1.0-h6caf38d_4.conda
  sha256: a2f2c1c2369360147c46f48124a3a17f5122e78543275ff9788dc91a1d5819dc
  md5: 4ce5651ae5cd6eebc5899f9bfe0eac3c
  depends:
  - __osx >=11.0
  - libbrotlicommon 1.1.0 h6caf38d_4
  license: MIT
  license_family: MIT
  purls: []
  size: 275791
  timestamp: 1756599724058
- conda: https://conda.anaconda.org/conda-forge/win-64/libbrotlienc-1.1.0-hfd05255_4.conda
  sha256: a593cde3e728a1e0486a19537846380e3ce90ae9d6c22c1412466a49474eeeed
  md5: 37f4669f8ac2f04d826440a8f3f42300
  depends:
  - libbrotlicommon 1.1.0 hfd05255_4
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls: []
  size: 245418
  timestamp: 1756599770744
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-35_he106b2a_openblas.conda
  build_number: 35
  sha256: 6f296f1567a7052c0f8b9527f74cfebc5418dbbae6dcdbae8659963f8ae7f48e
  md5: e62d58d32431dabed236c860dfa566ca
  depends:
  - libblas 3.9.0 35_h59b9bed_openblas
  constrains:
  - blas 2.135   openblas
  - liblapack  3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 16900
  timestamp: 1757002826333
- conda: https://conda.anaconda.org/conda-forge/osx-64/libcblas-3.9.0-35_hff6cab4_openblas.conda
  build_number: 35
  sha256: 1d4542fe690a7aa153dc7d8f691702812c179ed29d0a0e648f83830d7001456c
  md5: 096acef4d495c2454c3771034ce5d23a
  depends:
  - libblas 3.9.0 35_h7f60823_openblas
  constrains:
  - blas 2.135   openblas
  - liblapack  3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17132
  timestamp: 1757003606725
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-35_hb3479ef_openblas.conda
  build_number: 35
  sha256: dfd8dd4eea94894a01c1e4d9a409774ad2b71f77e713eb8c0dc62bb47abe2f0b
  md5: 7f8a6b52d39bde47c6f2d3ef96bd5e68
  depends:
  - libblas 3.9.0 35_h10e41b3_openblas
  constrains:
  - liblapacke 3.9.0   35*_openblas
  - blas 2.135   openblas
  - liblapack  3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17163
  timestamp: 1757003634172
- conda: https://conda.anaconda.org/conda-forge/win-64/libcblas-3.9.0-35_h2a3cdd5_mkl.conda
  build_number: 35
  sha256: 88939f6c1b5da75bd26ce663aa437e1224b26ee0dab5e60cecc77600975f397e
  md5: 9639091d266e92438582d0cc4cfc8350
  depends:
  - libblas 3.9.0 35_h5709861_mkl
  constrains:
  - blas 2.135   mkl
  - liblapack  3.9.0   35*_mkl
  - liblapacke 3.9.0   35*_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 66398
  timestamp: 1757003514529
- conda: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp20.1-20.1.8-default_h99862b1_1.conda
  sha256: d2aadd2b6c830256687e4caa945af24d5e8baac0ff25886c06cc9781b047461b
  md5: d6ff2e232c817e377856130eaceb7d2d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libllvm20 >=20.1.8,<20.2.0a0
  - libstdcxx >=14
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 21250549
  timestamp: 1757387452284
- conda: https://conda.anaconda.org/conda-forge/linux-64/libclang13-21.1.0-default_h746c552_1.conda
  sha256: e6c0123b888d6abf03c66c52ed89f9de1798dde930c5fd558774f26e994afbc6
  md5: 327c78a8ce710782425a89df851392f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libllvm21 >=21.1.0,<21.2.0a0
  - libstdcxx >=14
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 12358102
  timestamp: 1757383373129
- conda: https://conda.anaconda.org/conda-forge/win-64/libclang13-21.1.1-default_ha2db4b5_0.conda
  sha256: 6d73ef2edf64ff3759a380ed12bb1bf5a17d6035386c07377c34fbd6fa9c3d9d
  md5: 17f5b2e04b696f148b1b8ff1d5d55b75
  depends:
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 28988003
  timestamp: 1757621024964
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-hb8b1518_5.conda
  sha256: cb83980c57e311783ee831832eb2c20ecb41e7dee6e86e8b70b8cef0e43eab55
  md5: d4a250da4737ee127fb1fa6452a9002e
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 4523621
  timestamp: 1749905341688
- conda: https://conda.anaconda.org/conda-forge/osx-64/libcxx-21.1.0-h3d58e20_1.conda
  sha256: ff2c82c14232cc0ff8038b3d43dace4a792c05a9b01465448445ac52539dee40
  md5: d5bb255dcf8d208f30089a5969a0314b
  depends:
  - __osx >=10.13
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 572463
  timestamp: 1756698407882
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-21.1.0-hf598326_1.conda
  sha256: 58427116dc1b58b13b48163808daa46aacccc2c79d40000f8a3582938876fed7
  md5: 0fb2c0c9b1c1259bc7db75c1342b1d99
  depends:
  - __osx >=11.0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 568692
  timestamp: 1756698505599
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/osx-64/libdeflate-1.24-hcc1b750_0.conda
  sha256: 2733a4adf53daca1aa4f41fe901f0f8ee9e4c509abd23ffcd7660013772d6f45
  md5: f0a46c359722a3e84deb05cd4072d153
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 69751
  timestamp: 1747040526774
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libdeflate-1.24-h5773f1b_0.conda
  sha256: 417d52b19c679e1881cce3f01cad3a2d542098fa2d6df5485aac40f01aede4d1
  md5: 3baf58a5a87e7c2f4d243ce2f8f2fe5c
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 54790
  timestamp: 1747040549847
- conda: https://conda.anaconda.org/conda-forge/win-64/libdeflate-1.24-h76ddb4d_0.conda
  sha256: 65347475c0009078887ede77efe60db679ea06f2b56f7853b9310787fe5ad035
  md5: 08d988e266c6ae77e03d164b83786dc4
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 156292
  timestamp: 1747040812624
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb03c661_1.conda
  sha256: c076a213bd3676cc1ef22eeff91588826273513ccc6040d9bea68bccdc849501
  md5: 9314bc5a1fe7d1044dc9dfd3ef400535
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 310785
  timestamp: 1757212153962
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/osx-64/libedit-3.1.20250104-pl5321ha958ccf_0.conda
  sha256: 6cc49785940a99e6a6b8c6edbb15f44c2dd6c789d9c283e5ee7bdfedd50b4cd6
  md5: 1f4ed31220402fcddc083b4bff406868
  depends:
  - ncurses
  - __osx >=10.13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 115563
  timestamp: 1738479554273
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
  sha256: 66aa216a403de0bb0c1340a88d1a06adaff66bae2cfd196731aa24db9859d631
  md5: 44083d2d2c2025afca315c7a172eab2b
  depends:
  - ncurses
  - __osx >=11.0
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 107691
  timestamp: 1738479560845
- conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
  sha256: 7fd5408d359d05a969133e47af580183fbf38e2235b562193d427bb9dad79723
  md5: c151d5eb730e9b7480e6d48c0fc44048
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 44840
  timestamp: 1731330973553
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
  sha256: da2080da8f0288b95dd86765c801c6e166c4619b910b11f9a8446fb852438dc2
  md5: 4211416ecba1866fab0c6470986c22d6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 74811
  timestamp: 1752719572741
- conda: https://conda.anaconda.org/conda-forge/osx-64/libexpat-2.7.1-h21dd04a_0.conda
  sha256: 689862313571b62ee77ee01729dc093f2bf25a2f99415fcfe51d3a6cd31cce7b
  md5: 9fdeae0b7edda62e989557d645769515
  depends:
  - __osx >=10.13
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 72450
  timestamp: 1752719744781
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.1-hec049ff_0.conda
  sha256: 8fbb17a56f51e7113ed511c5787e0dec0d4b10ef9df921c4fd1cccca0458f648
  md5: b1ca5f21335782f71a8bd69bdc093f67
  depends:
  - __osx >=11.0
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 65971
  timestamp: 1752719657566
- conda: https://conda.anaconda.org/conda-forge/win-64/libexpat-2.7.1-hac47afa_0.conda
  sha256: 8432ca842bdf8073ccecf016ccc9140c41c7114dc4ec77ca754551c01f780845
  md5: 3608ffde260281fa641e70d6e34b1b96
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 141322
  timestamp: 1752719767870
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/osx-64/libffi-3.4.6-h281671d_1.conda
  sha256: 6394b1bc67c64a21a5cc73d1736d1d4193a64515152e861785c44d2cfc49edf3
  md5: 4ca9ea59839a9ca8df84170fab4ceb41
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 51216
  timestamp: 1743434595269
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
  sha256: c6a530924a9b14e193ea9adfe92843de2a806d1b7dbfd341546ece9653129e60
  md5: c215a60c2935b517dcda8cad4705734d
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 39839
  timestamp: 1743434670405
- conda: https://conda.anaconda.org/conda-forge/win-64/libffi-3.4.6-h537db12_1.conda
  sha256: d3b0b8812eab553d3464bbd68204f007f1ebadf96ce30eb0cbc5159f72e353f5
  md5: 85d8fa5e55ed8f93f874b3b23ed54ec6
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 44978
  timestamp: 1743435053850
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.14.1-ha770c72_0.conda
  sha256: 4641d37faeb97cf8a121efafd6afd040904d4bca8c46798122f417c31d5dfbec
  md5: f4084e4e6577797150f9b04a4560ceb0
  depends:
  - libfreetype6 >=2.14.1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 7664
  timestamp: 1757945417134
- conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype-2.14.1-h694c41f_0.conda
  sha256: 035e23ef87759a245d51890aedba0b494a26636784910c3730d76f3dc4482b1d
  md5: e0e2edaf5e0c71b843e25a7ecc451cc9
  depends:
  - libfreetype6 >=2.14.1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 7780
  timestamp: 1757945952392
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype-2.14.1-hce30654_0.conda
  sha256: 9de25a86066f078822d8dd95a83048d7dc2897d5d655c0e04a8a54fca13ef1ef
  md5: f35fb38e89e2776994131fbf961fa44b
  depends:
  - libfreetype6 >=2.14.1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 7810
  timestamp: 1757947168537
- conda: https://conda.anaconda.org/conda-forge/win-64/libfreetype-2.14.1-h57928b3_0.conda
  sha256: 2029702ec55e968ce18ec38cc8cf29f4c8c4989a0d51797164dab4f794349a64
  md5: 3235024fe48d4087721797ebd6c9d28c
  depends:
  - libfreetype6 >=2.14.1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 8109
  timestamp: 1757946135015
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.14.1-h73754d4_0.conda
  sha256: 4a7af818a3179fafb6c91111752954e29d3a2a950259c14a2fc7ba40a8b03652
  md5: 8e7251989bca326a28f4a5ffbd74557a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libpng >=1.6.50,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.14.1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 386739
  timestamp: 1757945416744
- conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype6-2.14.1-h6912278_0.conda
  sha256: f5f28092e368efc773bcd1c381d123f8b211528385a9353e36f8808d00d11655
  md5: dfbdc8fd781dc3111541e4234c19fdbd
  depends:
  - __osx >=10.13
  - libpng >=1.6.50,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.14.1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 374993
  timestamp: 1757945949585
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype6-2.14.1-h6da58f4_0.conda
  sha256: cc4aec4c490123c0f248c1acd1aeab592afb6a44b1536734e20937cda748f7cd
  md5: 6d4ede03e2a8e20eb51f7f681d2a2550
  depends:
  - __osx >=11.0
  - libpng >=1.6.50,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.14.1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 346703
  timestamp: 1757947166116
- conda: https://conda.anaconda.org/conda-forge/win-64/libfreetype6-2.14.1-hdbac1cb_0.conda
  sha256: 223710600b1a5567163f7d66545817f2f144e4ef8f84e99e90f6b8a4e19cb7ad
  md5: 6e7c5c5ab485057b5d07fd8188ba5c28
  depends:
  - libpng >=1.6.50,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - freetype >=2.14.1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 340264
  timestamp: 1757946133889
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_5.conda
  sha256: 0caed73aac3966bfbf5710e06c728a24c6c138605121a3dacb2e03440e8baa6a
  md5: 264fbfba7fb20acf3b29cde153e345ce
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 15.1.0 h767d61c_5
  - libgcc-ng ==15.1.0=*_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 824191
  timestamp: 1757042543820
- conda: https://conda.anaconda.org/conda-forge/win-64/libgcc-15.1.0-h1383e82_5.conda
  sha256: 9b997baa85ba495c04e1b30f097b80420c02dcaca6441c4bf2c6bb4b2c5d2114
  md5: c84381a01ede0e28d632fdbeea2debb2
  depends:
  - _openmp_mutex >=4.5
  - libwinpthread >=12.0.0.r4.gg4f2fc60ca
  constrains:
  - libgomp 15.1.0 h1383e82_5
  - msys2-conda-epoch <0.0a0
  - libgcc-ng ==15.1.0=*_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 668284
  timestamp: 1757042801517
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_5.conda
  sha256: f54bb9c3be12b24be327f4c1afccc2969712e0b091cdfbd1d763fb3e61cda03f
  md5: 069afdf8ea72504e48d23ae1171d951c
  depends:
  - libgcc 15.1.0 h767d61c_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29187
  timestamp: 1757042549554
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_5.conda
  sha256: 4c1a526198d0d62441549fdfd668cc8e18e77609da1e545bdcc771dd8dc6a990
  md5: 0c91408b3dec0b97e8a3c694845bd63b
  depends:
  - libgfortran5 15.1.0 hcea5267_5
  constrains:
  - libgfortran-ng ==15.1.0=*_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29169
  timestamp: 1757042575979
- conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran-15.1.0-h5f6db21_1.conda
  sha256: 844500c9372d455f6ae538ffd3cdd7fda5f53d25a2a6b3ba33060a302c37bc3e
  md5: 07cfad6b37da6e79349c6e3a0316a83b
  depends:
  - libgfortran5 15.1.0 hfa3c126_1
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 133973
  timestamp: 1756239628906
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-15.1.0-hfdf1602_1.conda
  sha256: 981e3fac416e80b007a2798d6c1d4357ebebeb72a039aca1fb3a7effe9dcae86
  md5: c98207b6e2b1a309abab696d229f163e
  depends:
  - libgfortran5 15.1.0 hb74de2c_1
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 134383
  timestamp: 1756239485494
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_5.conda
  sha256: 9d06adc6d8e8187ddc1cad87525c690bc8202d8cb06c13b76ab2fc80a35ed565
  md5: fbd4008644add05032b6764807ee2cba
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 1564589
  timestamp: 1757042559498
- conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran5-15.1.0-hfa3c126_1.conda
  sha256: c4bb79d9e9be3e3a335282b50d18a7965e2a972b95508ea47e4086f1fd699342
  md5: 696e408f36a5a25afdb23e862053ca82
  depends:
  - llvm-openmp >=8.0.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 1225193
  timestamp: 1756238834726
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-15.1.0-hb74de2c_1.conda
  sha256: 1f8f5b2fdd0d2559d0f3bade8da8f57e9ee9b54685bd6081c6d6d9a2b0239b41
  md5: 4281bd1c654cb4f5cab6392b3330451f
  depends:
  - llvm-openmp >=8.0.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 759679
  timestamp: 1756238772083
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
  sha256: dc2752241fa3d9e40ce552c1942d0a4b5eeb93740c9723873f6fcf8d39ef8d2d
  md5: 928b8be80851f5d8ffb016f9c81dae7a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - libglx 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 134712
  timestamp: 1731330998354
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.3-hf39c6af_0.conda
  sha256: e1ad3d9ddaa18f95ff5d244587fd1a37aca6401707f85a37f7d9b5002fcf16d0
  md5: 467f23819b1ea2b89c3fc94d65082301
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=14
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.3 *_0
  license: LGPL-2.1-or-later
  purls: []
  size: 3961899
  timestamp: 1754315006443
- conda: https://conda.anaconda.org/conda-forge/win-64/libglib-2.84.3-h1c1036b_0.conda
  sha256: bd322efaebc369e188a1dd93030325a40753a4c009e92c1f82ec481a20f0d232
  md5: 2bcc00752c158d4a70e1eaccbf6fe8ae
  depends:
  - libffi >=3.4.6,<3.5.0a0
  - libiconv >=1.18,<2.0a0
  - libintl >=0.22.5,<1.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - glib 2.84.3 *_0
  license: LGPL-2.1-or-later
  purls: []
  size: 3826069
  timestamp: 1754315362939
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
  sha256: 1175f8a7a0c68b7f81962699751bb6574e6f07db4c9f72825f978e3016f46850
  md5: 434ca7e50e40f4918ab701e3facd59a0
  depends:
  - __glibc >=2.17,<3.0.a0
  license: LicenseRef-libglvnd
  purls: []
  size: 132463
  timestamp: 1731330968309
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
  sha256: 2d35a679624a93ce5b3e9dd301fff92343db609b79f0363e6d0ceb3a6478bfa7
  md5: c8013e438185f33b13814c5c488acd5c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - xorg-libx11 >=1.8.10,<2.0a0
  license: LicenseRef-libglvnd
  purls: []
  size: 75504
  timestamp: 1731330988898
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_5.conda
  sha256: 125051d51a8c04694d0830f6343af78b556dd88cc249dfec5a97703ebfb1832d
  md5: dcd5ff1940cd38f6df777cac86819d60
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 447215
  timestamp: 1757042483384
- conda: https://conda.anaconda.org/conda-forge/win-64/libgomp-15.1.0-h1383e82_5.conda
  sha256: 65fd558d8f3296e364b8ae694932a64642fdd26d8eb4cf7adf08941e449be926
  md5: eae9a32a85152da8e6928a703a514d35
  depends:
  - libwinpthread >=12.0.0.r4.gg4f2fc60ca
  constrains:
  - msys2-conda-epoch <0.0a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 535560
  timestamp: 1757042749206
- conda: https://conda.anaconda.org/conda-forge/win-64/libhwloc-2.12.1-default_h88281d1_1000.conda
  sha256: 2fb437b82912c74b4869b66c601d52c77bb3ee8cb4812eab346d379f1c823225
  md5: e6298294e7612eccf57376a0683ddc80
  depends:
  - libwinpthread >=12.0.0.r4.gg4f2fc60ca
  - libxml2 >=2.13.8,<2.14.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 2412139
  timestamp: 1752762145331
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h3b78370_2.conda
  sha256: c467851a7312765447155e071752d7bf9bf44d610a5687e32706f480aad2833f
  md5: 915f5995e94f60e9a4826e0b0920ee88
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  license: LGPL-2.1-only
  purls: []
  size: 790176
  timestamp: 1754908768807
- conda: https://conda.anaconda.org/conda-forge/win-64/libiconv-1.18-hc1393d2_2.conda
  sha256: 0dcdb1a5f01863ac4e8ba006a8b0dc1a02d2221ec3319b5915a1863254d7efa7
  md5: 64571d1dd6cdcfa25d0664a5950fdaa2
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: LGPL-2.1-only
  purls: []
  size: 696926
  timestamp: 1754909290005
- conda: https://conda.anaconda.org/conda-forge/win-64/libintl-0.22.5-h5728263_3.conda
  sha256: c7e4600f28bcada8ea81456a6530c2329312519efcf0c886030ada38976b0511
  md5: 2cf0cf76cc15d360dfa2f17fd6cf9772
  depends:
  - libiconv >=1.17,<2.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 95568
  timestamp: 1723629479451
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  purls: []
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/osx-64/libjpeg-turbo-3.1.0-h6e16a3a_0.conda
  sha256: 9c0009389c1439ec96a08e3bf7731ac6f0eab794e0a133096556a9ae10be9c27
  md5: 87537967e6de2f885a9fcebd42b7cb10
  depends:
  - __osx >=10.13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  purls: []
  size: 586456
  timestamp: 1745268522731
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libjpeg-turbo-3.1.0-h5505292_0.conda
  sha256: 78df2574fa6aa5b6f5fc367c03192f8ddf8e27dc23641468d54e031ff560b9d4
  md5: 01caa4fbcaf0e6b08b3aef1151e91745
  depends:
  - __osx >=11.0
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  purls: []
  size: 553624
  timestamp: 1745268405713
- conda: https://conda.anaconda.org/conda-forge/win-64/libjpeg-turbo-3.1.0-h2466b09_0.conda
  sha256: e61b0adef3028b51251124e43eb6edf724c67c0f6736f1628b02511480ac354e
  md5: 7c51d27540389de84852daa1cdb9c63c
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  purls: []
  size: 838154
  timestamp: 1745268437136
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-35_h7ac8fdf_openblas.conda
  build_number: 35
  sha256: 3967e62d4d1d5c1492f861864afca95aaa8cac14e696ce7b9be7d0b6a50507e8
  md5: 88fa5489509c1da59ab2ee6b234511a5
  depends:
  - libblas 3.9.0 35_h59b9bed_openblas
  constrains:
  - blas 2.135   openblas
  - libcblas   3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 16920
  timestamp: 1757002835750
- conda: https://conda.anaconda.org/conda-forge/osx-64/liblapack-3.9.0-35_h236ab99_openblas.conda
  build_number: 35
  sha256: 16f4aaee86912defb0f695b5e6fca201cb5fe7e33c7d02bcb6a7d30ef2b28a39
  md5: 4ca37f2f14381acc4e5a0741b92aa14b
  depends:
  - libblas 3.9.0 35_h7f60823_openblas
  constrains:
  - blas 2.135   openblas
  - libcblas   3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17155
  timestamp: 1757003625275
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-35_hc9a63f6_openblas.conda
  build_number: 35
  sha256: 84f1c11187b564a9fdf464dad46d436ade966262e3000f7c5037b56b244f6fb8
  md5: 437d6c679b3d959d87b3b735fcc0b4ee
  depends:
  - libblas 3.9.0 35_h10e41b3_openblas
  constrains:
  - liblapacke 3.9.0   35*_openblas
  - libcblas   3.9.0   35*_openblas
  - blas 2.135   openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17166
  timestamp: 1757003647724
- conda: https://conda.anaconda.org/conda-forge/win-64/liblapack-3.9.0-35_hf9ab0e9_mkl.conda
  build_number: 35
  sha256: 56e0992fb58eed8f0d5fa165b8621fa150b84aa9af1467ea0a7a9bb7e2fced4f
  md5: 0c6ed9d722cecda18f50f17fb3c30002
  depends:
  - libblas 3.9.0 35_h5709861_mkl
  constrains:
  - blas 2.135   mkl
  - libcblas   3.9.0   35*_mkl
  - liblapacke 3.9.0   35*_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 78485
  timestamp: 1757003541803
- conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm20-20.1.8-hecd9e04_0.conda
  sha256: a6fddc510de09075f2b77735c64c7b9334cf5a26900da351779b275d9f9e55e1
  md5: 59a7b967b6ef5d63029b1712f8dcf661
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 43987020
  timestamp: 1752141980723
- conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm21-21.1.0-hecd9e04_0.conda
  sha256: d190f1bf322149321890908a534441ca2213a9a96c59819da6cabf2c5b474115
  md5: 9ad637a7ac380c442be142dfb0b1b955
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 44363060
  timestamp: 1756291822911
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/osx-64/liblzma-5.8.1-hd471939_2.conda
  sha256: 7e22fd1bdb8bf4c2be93de2d4e718db5c548aa082af47a7430eb23192de6bb36
  md5: 8468beea04b9065b9807fc8b9cdc5894
  depends:
  - __osx >=10.13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 104826
  timestamp: 1749230155443
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
  sha256: 0cb92a9e026e7bd4842f410a5c5c665c89b2eb97794ffddba519a626b8ce7285
  md5: d6df911d4564d77c4374b02552cb17d1
  depends:
  - __osx >=11.0
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 92286
  timestamp: 1749230283517
- conda: https://conda.anaconda.org/conda-forge/win-64/liblzma-5.8.1-h2466b09_2.conda
  sha256: 55764956eb9179b98de7cc0e55696f2eff8f7b83fc3ebff5e696ca358bca28cc
  md5: c15148b2e18da456f5108ccb5e411446
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 104935
  timestamp: 1749230611612
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
  sha256: 927fe72b054277cde6cb82597d0fcf6baf127dcbce2e0a9d8925a68f1265eef5
  md5: d864d34357c3b65a4b731f78c0801dc4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  license_family: GPL
  purls: []
  size: 33731
  timestamp: 1750274110928
- conda: https://conda.anaconda.org/conda-forge/linux-64/libntlm-1.8-hb9d3cd8_0.conda
  sha256: 3b3f19ced060013c2dd99d9d46403be6d319d4601814c772a3472fe2955612b0
  md5: 7c7927b404672409d9917d49bff5f2d6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 33418
  timestamp: 1734670021371
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_2.conda
  sha256: 1b51d1f96e751dc945cc06f79caa91833b0c3326efe24e9b506bd64ef49fc9b0
  md5: dfc5aae7b043d9f56ba99514d5e60625
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 5938936
  timestamp: 1755474342204
- conda: https://conda.anaconda.org/conda-forge/osx-64/libopenblas-0.3.30-openmp_h83c2472_2.conda
  sha256: 341dd45c2e88261f1f9ff76c3410355b4b0e894abe6ac89f7cbf64a3d10f0f01
  md5: 89edf77977f520c4245567460d065821
  depends:
  - __osx >=10.13
  - libgfortran
  - libgfortran5 >=14.3.0
  - llvm-openmp >=19.1.7
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6262457
  timestamp: 1755473612572
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_h60d53f8_2.conda
  sha256: 7b8551a4d21cf0b19f9a162f1f283a201b17f1bd5a6579abbd0d004788c511fa
  md5: d004259fd8d3d2798b16299d6ad6c9e9
  depends:
  - __osx >=11.0
  - libgfortran
  - libgfortran5 >=14.3.0
  - llvm-openmp >=19.1.7
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 4284696
  timestamp: 1755471861128
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopengl-1.7.0-ha4b6fd6_2.conda
  sha256: 215086c108d80349e96051ad14131b751d17af3ed2cb5a34edd62fa89bfe8ead
  md5: 7df50d44d4a14d6c31a2c54f2cd92157
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 50757
  timestamp: 1731330993524
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
  sha256: 0bd91de9b447a2991e666f284ae8c722ffb1d84acb594dbd0c031bd656fa32b2
  md5: 70e3400cbbfa03e96dcde7fc13e38c7b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 28424
  timestamp: 1749901812541
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h421ea60_1.conda
  sha256: e75a2723000ce3a4b9fd9b9b9ce77553556c93e475a4657db6ed01abc02ea347
  md5: 7af8e91b0deb5f8e25d1a595dea79614
  depends:
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 317390
  timestamp: 1753879899951
- conda: https://conda.anaconda.org/conda-forge/osx-64/libpng-1.6.50-h84aeda2_1.conda
  sha256: 8d92c82bcb09908008d8cf5fab75e20733810d40081261d57ef8cd6495fc08b4
  md5: 1fe32bb16991a24e112051cc0de89847
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 297609
  timestamp: 1753879919854
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libpng-1.6.50-h280e0eb_1.conda
  sha256: a2e0240fb0c79668047b528976872307ea80cb330baf8bf6624ac2c6443449df
  md5: 4d0f5ce02033286551a32208a5519884
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 287056
  timestamp: 1753879907258
- conda: https://conda.anaconda.org/conda-forge/win-64/libpng-1.6.50-h7351971_1.conda
  sha256: e84b041f91c94841cb9b97952ab7f058d001d4a15ed4ce226ec5fdb267cc0fa5
  md5: 3ae6e9f5c47c495ebeed95651518be61
  depends:
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 382709
  timestamp: 1753879944850
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpq-17.6-h3675c94_2.conda
  sha256: 8a078b33f65c5521ae1ed9545ea21efdc46630ff618cdd01aa6a3e698149b27d
  md5: e2c2f4c4c20a449b3b4a218797bd7c03
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=14
  - openldap >=2.6.10,<2.7.0a0
  - openssl >=3.5.2,<4.0a0
  license: PostgreSQL
  purls: []
  size: 2726071
  timestamp: 1757976008927
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
  sha256: 0105bd108f19ea8e6a78d2d994a6d4a8db16d19a41212070d2d1d48a63c34161
  md5: a587892d3c13b6621a6091be690dbca2
  depends:
  - libgcc-ng >=12
  license: ISC
  purls: []
  size: 205978
  timestamp: 1716828628198
- conda: https://conda.anaconda.org/conda-forge/osx-64/libsodium-1.0.20-hfdf4475_0.conda
  sha256: d3975cfe60e81072666da8c76b993af018cf2e73fe55acba2b5ba0928efaccf5
  md5: 6af4b059e26492da6013e79cbcb4d069
  depends:
  - __osx >=10.13
  license: ISC
  purls: []
  size: 210249
  timestamp: 1716828641383
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
  sha256: fade8223e1e1004367d7101dd17261003b60aa576df6d7802191f8972f7470b1
  md5: a7ce36e284c5faaf93c220dfc39e3abd
  depends:
  - __osx >=11.0
  license: ISC
  purls: []
  size: 164972
  timestamp: 1716828607917
- conda: https://conda.anaconda.org/conda-forge/win-64/libsodium-1.0.20-hc70643c_0.conda
  sha256: 7bcb3edccea30f711b6be9601e083ecf4f435b9407d70fc48fbcf9e5d69a0fc6
  md5: 198bb594f202b205c7d18b936fa4524f
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: ISC
  purls: []
  size: 202344
  timestamp: 1716828757533
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda
  sha256: 6d9c32fc369af5a84875725f7ddfbfc2ace795c28f246dc70055a79f9b2003da
  md5: 0b367fad34931cb79e0d6b7e5c06bb1c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  purls: []
  size: 932581
  timestamp: 1753948484112
- conda: https://conda.anaconda.org/conda-forge/osx-64/libsqlite-3.50.4-h39a8b3b_0.conda
  sha256: 466366b094c3eb4b1d77320530cbf5400e7a10ab33e4824c200147488eebf7a6
  md5: 156bfb239b6a67ab4a01110e6718cbc4
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  purls: []
  size: 980121
  timestamp: 1753948554003
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.4-h4237e3c_0.conda
  sha256: 802ebe62e6bc59fc26b26276b793e0542cfff2d03c086440aeaf72fb8bbcec44
  md5: 1dcb0468f5146e38fae99aef9656034b
  depends:
  - __osx >=11.0
  - icu >=75.1,<76.0a0
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  purls: []
  size: 902645
  timestamp: 1753948599139
- conda: https://conda.anaconda.org/conda-forge/win-64/libsqlite-3.50.4-hf5d6505_0.conda
  sha256: 5dc4f07b2d6270ac0c874caec53c6984caaaa84bc0d3eb593b0edf3dc8492efa
  md5: ccb20d946040f86f0c05b644d5eadeca
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: blessing
  purls: []
  size: 1288499
  timestamp: 1753948889360
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_5.conda
  sha256: 0f5f61cab229b6043541c13538d75ce11bd96fb2db76f94ecf81997b1fde6408
  md5: 4e02a49aaa9d5190cb630fa43528fbe6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 3896432
  timestamp: 1757042571458
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_5.conda
  sha256: 7b8cabbf0ab4fe3581ca28fe8ca319f964078578a51dd2ca3f703c1d21ba23ff
  md5: 8bba50c7f4679f08c861b597ad2bda6b
  depends:
  - libstdcxx 15.1.0 h8f9b012_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29233
  timestamp: 1757042603319
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-h8261f1e_6.conda
  sha256: c62694cd117548d810d2803da6d9063f78b1ffbf7367432c5388ce89474e9ebe
  md5: b6093922931b535a7ba566b6f384fbe6
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=14
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=14
  - libwebp-base >=1.6.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  purls: []
  size: 433078
  timestamp: 1755011934951
- conda: https://conda.anaconda.org/conda-forge/osx-64/libtiff-4.7.0-h59ddb5d_6.conda
  sha256: 656dc01238d4b766e35976319aba2a9b3ea707b467b7a5aad94ef49a150be7a8
  md5: 1cb7b8054ffa9460ca3dd782062f3074
  depends:
  - __osx >=10.13
  - lerc >=4.0.0,<5.0a0
  - libcxx >=19
  - libdeflate >=1.24,<1.25.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  purls: []
  size: 401676
  timestamp: 1755012183336
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libtiff-4.7.0-h025e3ab_6.conda
  sha256: d6ed4b307dde5d66b73aa3f155b3ed40ba9394947cfe148e2cd07605ef4b410b
  md5: d0862034c2c563ef1f52a3237c133d8d
  depends:
  - __osx >=11.0
  - lerc >=4.0.0,<5.0a0
  - libcxx >=19
  - libdeflate >=1.24,<1.25.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  purls: []
  size: 372136
  timestamp: 1755012109767
- conda: https://conda.anaconda.org/conda-forge/win-64/libtiff-4.7.0-h550210a_6.conda
  sha256: fd27821c8cfc425826f13760c3263d7b3b997c5372234cefa1586ff384dcc989
  md5: 72d45aa52ebca91aedb0cfd9eac62655
  depends:
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  purls: []
  size: 983988
  timestamp: 1755012056987
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb03c661_1.conda
  sha256: c180f4124a889ac343fc59d15558e93667d894a966ec6fdb61da1604481be26b
  md5: 0f03292cc56bf91a077a134ea8747118
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  license: MIT
  license_family: MIT
  purls: []
  size: 895108
  timestamp: 1753948278280
- conda: https://conda.anaconda.org/conda-forge/osx-64/libuv-1.51.0-h58003a5_1.conda
  sha256: d90dd0eee6f195a5bd14edab4c5b33be3635b674b0b6c010fb942b956aa2254c
  md5: fbfc6cf607ae1e1e498734e256561dc3
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 422612
  timestamp: 1753948458902
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libuv-1.51.0-h6caf38d_1.conda
  sha256: 042c7488ad97a5629ec0a991a8b2a3345599401ecc75ad6a5af73b60e6db9689
  md5: c0d87c3c8e075daf1daf6c31b53e8083
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 421195
  timestamp: 1753948426421
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.6.0-hd42ef1d_0.conda
  sha256: 3aed21ab28eddffdaf7f804f49be7a7d701e8f0e46c856d801270b470820a37b
  md5: aea31d2e5b1091feca96fcfe945c3cf9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - libwebp 1.6.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 429011
  timestamp: 1752159441324
- conda: https://conda.anaconda.org/conda-forge/osx-64/libwebp-base-1.6.0-hb807250_0.conda
  sha256: 00dbfe574b5d9b9b2b519acb07545380a6bc98d1f76a02695be4995d4ec91391
  md5: 7bb6608cf1f83578587297a158a6630b
  depends:
  - __osx >=10.13
  constrains:
  - libwebp 1.6.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 365086
  timestamp: 1752159528504
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libwebp-base-1.6.0-h07db88b_0.conda
  sha256: a4de3f371bb7ada325e1f27a4ef7bcc81b2b6a330e46fac9c2f78ac0755ea3dd
  md5: e5e7d467f80da752be17796b87fe6385
  depends:
  - __osx >=11.0
  constrains:
  - libwebp 1.6.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 294974
  timestamp: 1752159906788
- conda: https://conda.anaconda.org/conda-forge/win-64/libwebp-base-1.6.0-h4d5522a_0.conda
  sha256: 7b6316abfea1007e100922760e9b8c820d6fc19df3f42fb5aca684cfacb31843
  md5: f9bbae5e2537e3b06e0f7310ba76c893
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - libwebp 1.6.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 279176
  timestamp: 1752159543911
- conda: https://conda.anaconda.org/conda-forge/win-64/libwinpthread-12.0.0.r4.gg4f2fc60ca-h57928b3_9.conda
  sha256: 373f2973b8a358528b22be5e8d84322c165b4c5577d24d94fd67ad1bb0a0f261
  md5: 08bfa5da6e242025304b206d152479ef
  depends:
  - ucrt
  constrains:
  - pthreads-win32 <0.0a0
  - msys2-conda-epoch <0.0a0
  license: MIT AND BSD-3-Clause-Clear
  purls: []
  size: 35794
  timestamp: 1737099561703
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/osx-64/libxcb-1.17.0-hf1f96e2_0.conda
  sha256: 8896cd5deff6f57d102734f3e672bc17120613647288f9122bec69098e839af7
  md5: bbeca862892e2898bdb45792a61c4afc
  depends:
  - __osx >=10.13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 323770
  timestamp: 1727278927545
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libxcb-1.17.0-hdb1d25a_0.conda
  sha256: bd3816218924b1e43b275863e21a3e13a5db4a6da74cca8e60bc3c213eb62f71
  md5: af523aae2eca6dfa1c8eec693f5b9a79
  depends:
  - __osx >=11.0
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 323658
  timestamp: 1727278733917
- conda: https://conda.anaconda.org/conda-forge/win-64/libxcb-1.17.0-h0e4246c_0.conda
  sha256: 08dec73df0e161c96765468847298a420933a36bc4f09b50e062df8793290737
  md5: a69bbf778a462da324489976c84cfc8c
  depends:
  - libgcc >=13
  - libwinpthread >=12.0.0.r4.gg4f2fc60ca
  - pthread-stubs
  - ucrt >=10.0.20348.0
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 1208687
  timestamp: 1727279378819
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  purls: []
  size: 100393
  timestamp: 1702724383534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.11.0-he8b52b9_0.conda
  sha256: 23f47e86cc1386e7f815fa9662ccedae151471862e971ea511c5c886aa723a54
  md5: 74e91c36d0eef3557915c68b6c2bef96
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  purls: []
  size: 791328
  timestamp: 1754703902365
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h04c0eec_1.conda
  sha256: 03deb1ec6edfafc5aaeecadfc445ee436fecffcda11fcd97fde9b6632acb583f
  md5: 10bcbd05e1c1c9d652fccb42b776a9fa
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=14
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 698448
  timestamp: 1754315344761
- conda: https://conda.anaconda.org/conda-forge/win-64/libxml2-2.13.8-h741aa76_1.conda
  sha256: 32fa908bb2f2a6636dab0edaac1d4bf5ff62ad404a82d8bb16702bc5b8eb9114
  md5: aeb49dc1f5531de13d2c0d57ffa6d0c8
  depends:
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls: []
  size: 1519401
  timestamp: 1754315497781
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxslt-1.1.43-h7a3aeb2_0.conda
  sha256: 35ddfc0335a18677dd70995fa99b8f594da3beb05c11289c87b6de5b930b47a3
  md5: 31059dc620fa57d787e3899ed0421e6d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxml2 >=2.13.8,<2.14.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 244399
  timestamp: 1753273455036
- conda: https://conda.anaconda.org/conda-forge/win-64/libxslt-1.1.43-h25c3957_0.conda
  sha256: 20857f1adb91cc59826e146ee6cb1157c6abf2901a93d359b1106ba87c8e770b
  md5: e84f36aa02735c140099d992d491968d
  depends:
  - libxml2 >=2.13.8,<2.14.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 416974
  timestamp: 1753273998632
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/osx-64/libzlib-1.3.1-hd23fc13_2.conda
  sha256: 8412f96504fc5993a63edf1e211d042a1fd5b1d51dedec755d2058948fcced09
  md5: 003a54a4e32b02f7355b50a837e699da
  depends:
  - __osx >=10.13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 57133
  timestamp: 1727963183990
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
  sha256: ce34669eadaba351cd54910743e6a2261b67009624dbc7daeeafdef93616711b
  md5: 369964e85dc26bfe78f41399b366c435
  depends:
  - __osx >=11.0
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 46438
  timestamp: 1727963202283
- conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-1.3.1-h2466b09_2.conda
  sha256: ba945c6493449bed0e6e29883c4943817f7c79cbff52b83360f7b341277c6402
  md5: 41fbfac52c601159df6c01f875de31b9
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 55476
  timestamp: 1727963768015
- conda: https://conda.anaconda.org/conda-forge/osx-64/llvm-openmp-21.1.0-hf4e0ed4_0.conda
  sha256: 78336131a08990390003ef05d14ecb49f3a47e4dac60b1bcebeccd87fa402925
  md5: 5acc6c266fd33166fa3b33e48665ae0d
  depends:
  - __osx >=10.13
  constrains:
  - openmp 21.1.0|21.1.0.*
  - intel-openmp <0.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  purls: []
  size: 311174
  timestamp: 1756673275570
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-21.1.0-hbb9b287_0.conda
  sha256: c6750073a128376a14bedacfa90caab4c17025c9687fcf6f96e863b28d543af4
  md5: e57d95fec6eaa747e583323cba6cfe5c
  depends:
  - __osx >=11.0
  constrains:
  - intel-openmp <0.0a0
  - openmp 21.1.0|21.1.0.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  purls: []
  size: 286039
  timestamp: 1756673290280
- conda: https://conda.anaconda.org/conda-forge/win-64/llvm-openmp-20.1.8-hfa2b4ca_2.conda
  sha256: 8970b7f9057a1c2c18bfd743c6f5ce73b86197d7724423de4fa3d03911d5874b
  md5: 2dc2edf349464c8b83a576175fc2ad42
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - intel-openmp <0.0a0
  - openmp 20.1.8|20.1.8.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  purls: []
  size: 344490
  timestamp: 1756145011384
- conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py311h2dc5d0c_1.conda
  sha256: 0291d90706ac6d3eea73e66cd290ef6d805da3fad388d1d476b8536ec92ca9a8
  md5: 6565a715337ae279e351d0abd8ffe88a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/markupsafe?source=hash-mapping
  size: 25354
  timestamp: 1733219879408
- conda: https://conda.anaconda.org/conda-forge/osx-64/markupsafe-3.0.2-py311ha3cf9ac_1.conda
  sha256: e9965b5d4c29b17b1512035b24a7c126ed7bdb6b39103b52cae099d5bb4194a9
  md5: 1d6596ca7c7b66215c5c0d58b3cb0dd3
  depends:
  - __osx >=10.13
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/markupsafe?source=hash-mapping
  size: 24688
  timestamp: 1733219887972
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/markupsafe-3.0.2-py311h4921393_1.conda
  sha256: 4f738a7c80e34e5e5d558e946b06d08e7c40e3cc4bdf08140bf782c359845501
  md5: 249e2f6f5393bb6b36b3d3a3eebdcdf9
  depends:
  - __osx >=11.0
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/markupsafe?source=hash-mapping
  size: 24976
  timestamp: 1733219849253
- conda: https://conda.anaconda.org/conda-forge/win-64/markupsafe-3.0.2-py311h5082efb_1.conda
  sha256: 6f756e13ccf1a521d3960bd3cadddf564e013e210eaeced411c5259f070da08e
  md5: c1f2ddad665323278952a453912dc3bd
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/markupsafe?source=hash-mapping
  size: 28238
  timestamp: 1733220208800
- conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-3.10.6-py311h38be061_1.conda
  sha256: c40fdf402bb4433d242204d89575ccd02a725544c15703c969eb33881f7e26d5
  md5: 0783509652a221fb6e7910ab65799b89
  depends:
  - matplotlib-base >=3.10.6,<3.10.7.0a0
  - pyside6 >=6.7.2
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - tornado >=5
  license: PSF-2.0
  license_family: PSF
  purls: []
  size: 17468
  timestamp: 1756869735950
- conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-3.10.6-py311h6eed73b_1.conda
  sha256: 5cb37979a6631d11d7c2edc5e41e0738b65764747146d218db14a4ba59c38d4a
  md5: e2196d2b3e03063ef76952439ae2ddfc
  depends:
  - matplotlib-base >=3.10.6,<3.10.7.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - tornado >=5
  license: PSF-2.0
  license_family: PSF
  purls: []
  size: 17498
  timestamp: 1756870059719
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-3.10.6-py311ha1ab1f8_0.conda
  sha256: 3303938df2916a05e105bb60265c6b0c215923f4ecdc4c06883e661367d9d5ce
  md5: 434c94fe4f1feb21066a92eabeb3dfaf
  depends:
  - matplotlib-base >=3.10.6,<3.10.7.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - tornado >=5
  license: PSF-2.0
  license_family: PSF
  purls: []
  size: 17463
  timestamp: 1756835704502
- conda: https://conda.anaconda.org/conda-forge/win-64/matplotlib-3.10.6-py311h1ea47a8_1.conda
  sha256: 9a24dbc1ed9d5721bfba431cc3e095c11f0afba35416abcb6d93333cb3a991d6
  md5: 9134d619eb75f5ba071a469a7eda1048
  depends:
  - matplotlib-base >=3.10.6,<3.10.7.0a0
  - pyside6 >=6.7.2
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - tornado >=5
  license: PSF-2.0
  license_family: PSF
  purls: []
  size: 17783
  timestamp: 1756870090452
- conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.6-py311h0f3be63_1.conda
  sha256: 504ff6c8b4d5fba670b9b518d6e4ed7149a9c3682cb495fe9f87c79d387b2992
  md5: 65ddf155ea43aa3bb366cb03ddf3436a
  depends:
  - __glibc >=2.17,<3.0.a0
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=14
  - libstdcxx >=14
  - numpy >=1.23
  - numpy >=1.23,<3
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.11,<3.12.0a0
  - python-dateutil >=2.7
  - python_abi 3.11.* *_cp311
  - qhull >=2020.2,<2020.3.0a0
  - tk >=8.6.13,<8.7.0a0
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/matplotlib?source=hash-mapping
  size: 8484360
  timestamp: 1756869716686
- conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-base-3.10.6-py311h48d7e91_1.conda
  sha256: d808d67d497f77376139c98fa2b5d9bcfcf6c12c24fa57d55b29ad1f569de30f
  md5: 0d9bcc9297c8e179ee7e43b7af3b543a
  depends:
  - __osx >=10.13
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libcxx >=19
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - numpy >=1.23
  - numpy >=1.23,<3
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.11,<3.12.0a0
  - python-dateutil >=2.7
  - python_abi 3.11.* *_cp311
  - qhull >=2020.2,<2020.3.0a0
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/matplotlib?source=hash-mapping
  size: 8271784
  timestamp: 1756870011797
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-base-3.10.6-py311h66dac5a_0.conda
  sha256: 198ddbc9aa2a86987b87ebbcee9db2a5a1716bdd31ffd96c118f403e003e0474
  md5: 5f6150ef5ff86560f9b77c8d1aa20b96
  depends:
  - __osx >=11.0
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libcxx >=19
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - numpy >=1.23
  - numpy >=1.23,<3
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python-dateutil >=2.7
  - python_abi 3.11.* *_cp311
  - qhull >=2020.2,<2020.3.0a0
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/matplotlib?source=hash-mapping
  size: 8225464
  timestamp: 1756835662332
- conda: https://conda.anaconda.org/conda-forge/win-64/matplotlib-base-3.10.6-py311h1675fdf_1.conda
  sha256: ddf4dffdf128835c6feeb2e3c10e757802a4605adadafc15bed876dd6b75620a
  md5: 076c701b94086fced31eef41e80927b0
  depends:
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - numpy >=1.23
  - numpy >=1.23,<3
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.11,<3.12.0a0
  - python-dateutil >=2.7
  - python_abi 3.11.* *_cp311
  - qhull >=2020.2,<2020.3.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/matplotlib?source=hash-mapping
  size: 8049626
  timestamp: 1756870061088
- conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
  sha256: 69b7dc7131703d3d60da9b0faa6dd8acbf6f6c396224cf6aef3e855b8c0c41c6
  md5: af6ab708897df59bd6e7283ceab1b56b
  depends:
  - python >=3.9
  - traitlets
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/matplotlib-inline?source=hash-mapping
  size: 14467
  timestamp: 1733417051523
- conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.4-pyhcf101f3_0.conda
  sha256: 609ea628ace5c6cdbdce772704e6cb159ead26969bb2f386ca1757632b0f74c6
  md5: f5a4d548d1d3bdd517260409fc21e205
  depends:
  - python >=3.10
  - typing_extensions
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/mistune?source=hash-mapping
  size: 72996
  timestamp: 1756495311698
- conda: https://conda.anaconda.org/conda-forge/win-64/mkl-2024.2.2-h57928b3_16.conda
  sha256: ce841e7c3898764154a9293c0f92283c1eb28cdacf7a164c94b632a6af675d91
  md5: 5cddc979c74b90cf5e5cda4f97d5d8bb
  depends:
  - llvm-openmp >=20.1.8
  - tbb 2021.*
  license: LicenseRef-IntelSimplifiedSoftwareOct2022
  license_family: Proprietary
  purls: []
  size: 103088799
  timestamp: 1753975600547
- conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
  sha256: d09c47c2cf456de5c09fa66d2c3c5035aa1fa228a1983a433c47b876aa16ce90
  md5: 37293a85a0f4f77bbd9cf7aaefc62609
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/munkres?source=hash-mapping
  size: 15851
  timestamp: 1749895533014
- conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
  sha256: a20cff739d66c2f89f413e4ba4c6f6b59c50d5c30b5f0d840c13e8c9c2df9135
  md5: 6bb0d77277061742744176ab555b723c
  depends:
  - jupyter_client >=6.1.12
  - jupyter_core >=4.12,!=5.0.*
  - nbformat >=5.1
  - python >=3.8
  - traitlets >=5.4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/nbclient?source=hash-mapping
  size: 28045
  timestamp: 1734628936013
- conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
  sha256: dcccb07c5a1acb7dc8be94330e62d54754c0e9c9cb2bb6865c8e3cfe44cf5a58
  md5: d24beda1d30748afcc87c429454ece1b
  depends:
  - beautifulsoup4
  - bleach-with-css !=5.0.0
  - defusedxml
  - importlib-metadata >=3.6
  - jinja2 >=3.0
  - jupyter_core >=4.7
  - jupyterlab_pygments
  - markupsafe >=2.0
  - mistune >=2.0.3,<4
  - nbclient >=0.5.0
  - nbformat >=5.7
  - packaging
  - pandocfilters >=1.4.1
  - pygments >=2.4.1
  - python >=3.9
  - traitlets >=5.1
  - python
  constrains:
  - pandoc >=2.9.2,<4.0.0
  - nbconvert ==7.16.6 *_0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/nbconvert?source=hash-mapping
  size: 200601
  timestamp: 1738067871724
- conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
  sha256: 7a5bd30a2e7ddd7b85031a5e2e14f290898098dc85bea5b3a5bf147c25122838
  md5: bbe1963f1e47f594070ffe87cdf612ea
  depends:
  - jsonschema >=2.6
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-fastjsonschema >=2.15
  - traitlets >=5.1
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/nbformat?source=hash-mapping
  size: 100945
  timestamp: 1733402844974
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/osx-64/ncurses-6.5-h0622a9a_3.conda
  sha256: ea4a5d27ded18443749aefa49dc79f6356da8506d508b5296f60b8d51e0c4bd9
  md5: ced34dd9929f491ca6dab6a2927aff25
  depends:
  - __osx >=10.13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 822259
  timestamp: 1738196181298
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
  sha256: 2827ada40e8d9ca69a153a45f7fd14f32b2ead7045d3bbb5d10964898fe65733
  md5: 068d497125e4bf8a66bf707254fff5ae
  depends:
  - __osx >=11.0
  license: X11 AND BSD-3-Clause
  purls: []
  size: 797030
  timestamp: 1738196177597
- conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
  sha256: bb7b21d7fd0445ddc0631f64e66d91a179de4ba920b8381f29b9d006a42788c0
  md5: 598fd7d4d0de2455fb74f56063969a97
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/nest-asyncio?source=hash-mapping
  size: 11543
  timestamp: 1733325673691
- conda: https://conda.anaconda.org/conda-forge/linux-64/nodejs-24.4.1-heeeca48_0.conda
  sha256: 1239ba36ea69eefcc55f107fe186810b59488923544667175f6976fa4903c8c9
  md5: d629b201c3fbc0c203ca0ad7b03f22ce
  depends:
  - libgcc >=14
  - __glibc >=2.28,<3.0.a0
  - libstdcxx >=14
  - libgcc >=14
  - libuv >=1.51.0,<2.0a0
  - icu >=75.1,<76.0a0
  - openssl >=3.5.1,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 25669735
  timestamp: 1752839464718
- conda: https://conda.anaconda.org/conda-forge/osx-64/nodejs-24.4.1-h2e7699b_0.conda
  sha256: 1c9571726b5b5e85acfba50dda7ae9b22d2b29e590159a581bafde5bf2e04621
  md5: 9993063cfe84cf1fa928c7d021bd01a0
  depends:
  - __osx >=10.15
  - libcxx >=19
  - openssl >=3.5.1,<4.0a0
  - libuv >=1.51.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - icu >=75.1,<76.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 18918546
  timestamp: 1752839437994
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/nodejs-24.4.1-hab9d20b_0.conda
  sha256: c79d2c81f80a9adedc77362f2e8b10879ed0f9806deb6ba2464c1287a05f0b9b
  md5: 463a537de602f8558604f27395b323d0
  depends:
  - libcxx >=19
  - __osx >=11.0
  - openssl >=3.5.1,<4.0a0
  - libuv >=1.51.0,<2.0a0
  - icu >=75.1,<76.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 17949155
  timestamp: 1752839389217
- conda: https://conda.anaconda.org/conda-forge/win-64/nodejs-24.4.1-he453025_0.conda
  sha256: 1bb0d9e370bb0ffa2071ccfdd0ef3cb90bd183b07c67b646d1aa5c743004d233
  md5: cde0d5793a73ab343b5764fa6c002771
  license: MIT
  license_family: MIT
  purls: []
  size: 29967122
  timestamp: 1752839409586
- conda: https://conda.anaconda.org/conda-forge/noarch/notebook-7.4.5-pyhd8ed1ab_0.conda
  sha256: ea9d7058d862530755abeb2ee8f0152453cf630b024c73906f689ca1c297cd79
  md5: 28062c17cdb444388c00903eaec1ba0e
  depends:
  - jupyter_server >=2.4.0,<3
  - jupyterlab >=4.4.5,<4.5
  - jupyterlab_server >=2.27.1,<3
  - notebook-shim >=0.2,<0.3
  - python >=3.9
  - tornado >=6.2.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/notebook?source=hash-mapping
  size: 10349114
  timestamp: 1754404047534
- conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
  sha256: 7b920e46b9f7a2d2aa6434222e5c8d739021dbc5cc75f32d124a8191d86f9056
  md5: e7f89ea5f7ea9401642758ff50a2d9c1
  depends:
  - jupyter_server >=1.8,<3
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/notebook-shim?source=hash-mapping
  size: 16817
  timestamp: 1733408419340
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py311h2e04523_2.conda
  sha256: a8be67f8be3354e33da3adacb021f7f93971ddc466d79eab412151bf5dc58cf4
  md5: 7fb6248106c55e3ccd29ac675be01fac
  depends:
  - python
  - libgcc >=14
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libcblas >=3.9.0,<4.0a0
  - libblas >=3.9.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=hash-mapping
  size: 9415269
  timestamp: 1756343065236
- conda: https://conda.anaconda.org/conda-forge/osx-64/numpy-2.3.2-py311h09fcace_2.conda
  sha256: 7db239350fcce5f16bcb2891578be74a17cea9aa61506b427d3d0fc528b0a18f
  md5: 3fd67cc81b4297d403e3bfde14ab409e
  depends:
  - python
  - __osx >=10.13
  - libcxx >=19
  - liblapack >=3.9.0,<4.0a0
  - libblas >=3.9.0,<4.0a0
  - python_abi 3.11.* *_cp311
  - libcblas >=3.9.0,<4.0a0
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=hash-mapping
  size: 8551576
  timestamp: 1756343060935
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.2-py311h0856f98_2.conda
  sha256: b2cb7fc23d462840fec513fbb5ca4467a2d8b3e9ab2588096443b80d9f286058
  md5: c55c144bf8ee31580fe4fecaab9043a5
  depends:
  - python
  - python 3.11.* *_cpython
  - __osx >=11.0
  - libcxx >=19
  - liblapack >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libblas >=3.9.0,<4.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=compressed-mapping
  size: 7274422
  timestamp: **********339
- conda: https://conda.anaconda.org/conda-forge/win-64/numpy-2.3.2-py311h80b3fa1_2.conda
  sha256: cfce275b0959d2de5abeaa5892ec3192a3aa38600c8e12d962c9c4a4ac838106
  md5: 7ad07d0ab1888f4e4dc95b330a51bffd
  depends:
  - python
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - libblas >=3.9.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=hash-mapping
  size: 8019925
  timestamp: 1756343088463
- pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
  name: openai
  version: 1.106.1
  sha256: bfdef37c949f80396c59f2c17e0eda35414979bc07ef3379596a93c9ed044f3a
  requires_dist:
  - anyio>=3.5.0,<5
  - distro>=1.7.0,<2
  - httpx>=0.23.0,<1
  - jiter>=0.4.0,<1
  - pydantic>=1.9.0,<3
  - sniffio
  - tqdm>4
  - typing-extensions>=4.11,<5
  - aiohttp ; extra == 'aiohttp'
  - httpx-aiohttp>=0.1.8 ; extra == 'aiohttp'
  - numpy>=1 ; extra == 'datalib'
  - pandas-stubs>=******** ; extra == 'datalib'
  - pandas>=1.2.3 ; extra == 'datalib'
  - websockets>=13,<16 ; extra == 'realtime'
  - numpy>=2.0.2 ; extra == 'voice-helpers'
  - sounddevice>=0.5.1 ; extra == 'voice-helpers'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h55fea9a_1.conda
  sha256: 0b7396dacf988f0b859798711b26b6bc9c6161dca21bacfd778473da58730afa
  md5: 01243c4aaf71bde0297966125aea4706
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libpng >=1.6.50,<1.7.0a0
  - libstdcxx >=14
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 357828
  timestamp: 1754297886899
- conda: https://conda.anaconda.org/conda-forge/osx-64/openjpeg-2.5.3-h036ada5_1.conda
  sha256: fea2a79edb123fda31d73857e96b6cd24404a31d41693d8ef41235caed74b28e
  md5: 38f264b121a043cf379980c959fb2d75
  depends:
  - __osx >=10.13
  - libcxx >=19
  - libpng >=1.6.50,<1.7.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 336370
  timestamp: 1754297904811
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/openjpeg-2.5.3-h889cd5d_1.conda
  sha256: 6013916893fcd9bc97c479279cfe4616de7735ec566bad0ee41bc729e14d31b2
  md5: ab581998c77c512d455a13befcddaac3
  depends:
  - __osx >=11.0
  - libcxx >=19
  - libpng >=1.6.50,<1.7.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 320198
  timestamp: 1754297986425
- conda: https://conda.anaconda.org/conda-forge/win-64/openjpeg-2.5.3-h24db6dd_1.conda
  sha256: c29cb1641bc5cfc2197e9b7b436f34142be4766dd2430a937b48b7474935aa55
  md5: 25f45acb1a234ad1c9b9a20e1e6c559e
  depends:
  - libpng >=1.6.50,<1.7.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 245076
  timestamp: 1754298075628
- conda: https://conda.anaconda.org/conda-forge/linux-64/openldap-2.6.10-he970967_0.conda
  sha256: cb0b07db15e303e6f0a19646807715d28f1264c6350309a559702f4f34f37892
  md5: 2e5bf4f1da39c0b32778561c3c4e5878
  depends:
  - __glibc >=2.17,<3.0.a0
  - cyrus-sasl >=2.1.27,<3.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.5.0,<4.0a0
  license: OLDAP-2.8
  license_family: BSD
  purls: []
  size: 780253
  timestamp: 1748010165522
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.2-h26f9b46_0.conda
  sha256: c9f54d4e8212f313be7b02eb962d0cb13a8dae015683a403d3accd4add3e520e
  md5: ffffb341206dd0dab0c36053c048d621
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=14
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3128847
  timestamp: 1754465526100
- conda: https://conda.anaconda.org/conda-forge/osx-64/openssl-3.5.2-h6e31bce_0.conda
  sha256: 8be57a11019666aa481122c54e29afd604405b481330f37f918e9fbcd145ef89
  md5: 22f5d63e672b7ba467969e9f8b740ecd
  depends:
  - __osx >=10.13
  - ca-certificates
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 2743708
  timestamp: 1754466962243
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.2-he92f556_0.conda
  sha256: f6d1c87dbcf7b39fad24347570166dade1c533ae2d53c60a70fa4dc874ef0056
  md5: bcb0d87dfbc199d0a461d2c7ca30b3d8
  depends:
  - __osx >=11.0
  - ca-certificates
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3074848
  timestamp: 1754465710470
- conda: https://conda.anaconda.org/conda-forge/win-64/openssl-3.5.2-h725018a_0.conda
  sha256: 2413f3b4606018aea23acfa2af3c4c46af786739ab4020422e9f0c2aec75321b
  md5: 150d3920b420a27c0848acca158f94dc
  depends:
  - ca-certificates
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 9275175
  timestamp: 1754467904482
- conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
  sha256: 1840bd90d25d4930d60f57b4f38d4e0ae3f5b8db2819638709c36098c6ba770c
  md5: e51f1e4089cad105b6cac64bd8166587
  depends:
  - python >=3.9
  - typing_utils
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/overrides?source=hash-mapping
  size: 30139
  timestamp: 1734587755455
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
  sha256: 289861ed0c13a15d7bbb408796af4de72c2fe67e2bcb0de98f4c3fce259d7991
  md5: 58335b26c38bf4a20f399384c33cbcf9
  depends:
  - python >=3.8
  - python
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/packaging?source=hash-mapping
  size: 62477
  timestamp: 1745345660407
- conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.2-py311hed34c8f_0.conda
  sha256: ac5372b55c12644ba4bab81270bb294fb70197f86c9b3ede57dfe367ecc6f198
  md5: f98711aba4ad00ea3c286dcea5f57c1f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - numpy >=1.22.4
  - numpy >=1.23,<3
  - python >=3.11,<3.12.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.11.* *_cp311
  - pytz >=2020.1
  constrains:
  - pyqt5 >=5.15.9
  - pyarrow >=10.0.1
  - s3fs >=2022.11.0
  - xlrd >=2.0.1
  - numexpr >=2.8.4
  - openpyxl >=3.1.0
  - pyreadstat >=1.2.0
  - zstandard >=0.19.0
  - bottleneck >=1.3.6
  - python-calamine >=0.1.7
  - fastparquet >=2022.12.0
  - psycopg2 >=2.9.6
  - html5lib >=1.1
  - numba >=0.56.4
  - lxml >=4.9.2
  - odfpy >=1.4.1
  - xlsxwriter >=3.0.5
  - pytables >=3.8.0
  - pandas-gbq >=0.19.0
  - sqlalchemy >=2.0.0
  - blosc >=1.21.3
  - pyxlsb >=1.0.10
  - tabulate >=0.9.0
  - tzdata >=2022.7
  - gcsfs >=2022.11.0
  - fsspec >=2022.11.0
  - scipy >=1.10.0
  - beautifulsoup4 >=4.11.2
  - qtpy >=2.3.0
  - matplotlib >=3.6.3
  - xarray >=2022.12.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pandas?source=compressed-mapping
  size: 15354460
  timestamp: 1755779368955
- conda: https://conda.anaconda.org/conda-forge/osx-64/pandas-2.3.2-py311hf4bc098_0.conda
  sha256: b61681152840b4690d714ef1c4d2ef1dfd1985e0fc5d5d811bcb629c484d2466
  md5: 8df328d2c2b8f76d94c87c848a7b49a6
  depends:
  - __osx >=10.13
  - libcxx >=19
  - numpy >=1.22.4
  - numpy >=1.23,<3
  - python >=3.11,<3.12.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.11.* *_cp311
  - pytz >=2020.1
  constrains:
  - numexpr >=2.8.4
  - fastparquet >=2022.12.0
  - qtpy >=2.3.0
  - scipy >=1.10.0
  - sqlalchemy >=2.0.0
  - lxml >=4.9.2
  - html5lib >=1.1
  - matplotlib >=3.6.3
  - odfpy >=1.4.1
  - xlrd >=2.0.1
  - beautifulsoup4 >=4.11.2
  - fsspec >=2022.11.0
  - numba >=0.56.4
  - blosc >=1.21.3
  - pyqt5 >=5.15.9
  - gcsfs >=2022.11.0
  - tzdata >=2022.7
  - xarray >=2022.12.0
  - tabulate >=0.9.0
  - psycopg2 >=2.9.6
  - pyxlsb >=1.0.10
  - bottleneck >=1.3.6
  - pytables >=3.8.0
  - xlsxwriter >=3.0.5
  - zstandard >=0.19.0
  - openpyxl >=3.1.0
  - python-calamine >=0.1.7
  - pandas-gbq >=0.19.0
  - pyreadstat >=1.2.0
  - pyarrow >=10.0.1
  - s3fs >=2022.11.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pandas?source=hash-mapping
  size: 14631576
  timestamp: 1755779827936
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pandas-2.3.2-py311hff7e5bb_0.conda
  sha256: eb63f2d792b8eb69d1d53750fdde083f0bcf5b928cb069a1e557016a01ce4d71
  md5: b28dbf599ee12914447e0b60530950d2
  depends:
  - __osx >=11.0
  - libcxx >=19
  - numpy >=1.22.4
  - numpy >=1.23,<3
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.11.* *_cp311
  - pytz >=2020.1
  constrains:
  - pandas-gbq >=0.19.0
  - zstandard >=0.19.0
  - pyarrow >=10.0.1
  - lxml >=4.9.2
  - xarray >=2022.12.0
  - xlsxwriter >=3.0.5
  - pytables >=3.8.0
  - pyreadstat >=1.2.0
  - psycopg2 >=2.9.6
  - odfpy >=1.4.1
  - qtpy >=2.3.0
  - openpyxl >=3.1.0
  - html5lib >=1.1
  - fastparquet >=2022.12.0
  - python-calamine >=0.1.7
  - sqlalchemy >=2.0.0
  - beautifulsoup4 >=4.11.2
  - scipy >=1.10.0
  - pyqt5 >=5.15.9
  - gcsfs >=2022.11.0
  - blosc >=1.21.3
  - bottleneck >=1.3.6
  - s3fs >=2022.11.0
  - pyxlsb >=1.0.10
  - xlrd >=2.0.1
  - fsspec >=2022.11.0
  - tabulate >=0.9.0
  - matplotlib >=3.6.3
  - numexpr >=2.8.4
  - tzdata >=2022.7
  - numba >=0.56.4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pandas?source=hash-mapping
  size: 14406952
  timestamp: 1755779878619
- conda: https://conda.anaconda.org/conda-forge/win-64/pandas-2.3.2-py311h11fd7f3_0.conda
  sha256: 7eaadbdb9c58274daac8f5659ce448a570ea10e9bfc55c97a72a95a6e9b4d5aa
  md5: 1528d744a31b20442ca7c1f365a28cc2
  depends:
  - numpy >=1.22.4
  - numpy >=1.23,<3
  - python >=3.11,<3.12.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.11.* *_cp311
  - pytz >=2020.1
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - beautifulsoup4 >=4.11.2
  - tzdata >=2022.7
  - xlsxwriter >=3.0.5
  - pytables >=3.8.0
  - html5lib >=1.1
  - s3fs >=2022.11.0
  - matplotlib >=3.6.3
  - pyarrow >=10.0.1
  - lxml >=4.9.2
  - python-calamine >=0.1.7
  - pyxlsb >=1.0.10
  - pandas-gbq >=0.19.0
  - fastparquet >=2022.12.0
  - bottleneck >=1.3.6
  - xarray >=2022.12.0
  - fsspec >=2022.11.0
  - odfpy >=1.4.1
  - pyqt5 >=5.15.9
  - openpyxl >=3.1.0
  - tabulate >=0.9.0
  - psycopg2 >=2.9.6
  - blosc >=1.21.3
  - pyreadstat >=1.2.0
  - qtpy >=2.3.0
  - scipy >=1.10.0
  - gcsfs >=2022.11.0
  - numexpr >=2.8.4
  - sqlalchemy >=2.0.0
  - zstandard >=0.19.0
  - xlrd >=2.0.1
  - numba >=0.56.4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pandas?source=hash-mapping
  size: 14410335
  timestamp: 1755779632554
- conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
  sha256: 2bb9ba9857f4774b85900c2562f7e711d08dd48e2add9bee4e1612fbee27e16f
  md5: 457c2c8c08e54905d6954e79cb5b5db9
  depends:
  - python !=3.0,!=3.1,!=3.2,!=3.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pandocfilters?source=hash-mapping
  size: 11627
  timestamp: 1631603397334
- conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.5-pyhcf101f3_0.conda
  sha256: 30de7b4d15fbe53ffe052feccde31223a236dae0495bab54ab2479de30b2990f
  md5: a110716cdb11cf51482ff4000dc253d7
  depends:
  - python >=3.10
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/parso?source=hash-mapping
  size: 81562
  timestamp: 1755974222274
- conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
  sha256: ab52916f056b435757d46d4ce0a93fd73af47df9c11fd72b74cc4b7e1caca563
  md5: ee23fabfd0a8c6b8d6f3729b47b2859d
  depends:
  - numpy >=1.4.0
  - python >=3.9
  license: BSD-2-Clause AND PSF-2.0
  license_family: BSD
  purls:
  - pkg:pypi/patsy?source=hash-mapping
  size: 186594
  timestamp: 1733792482894
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
  sha256: 27c4014f616326240dcce17b5f3baca3953b6bc5f245ceb49c3fa1e6320571eb
  md5: b90bece58b4c2bf25969b70f3be42d25
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1197308
  timestamp: 1745955064657
- conda: https://conda.anaconda.org/conda-forge/win-64/pcre2-10.45-h99c9b8b_0.conda
  sha256: 165d6f76e7849615cfa5fe5f0209b90103102db17a7b4632f933fa9c0e8d8bfe
  md5: f4c483274001678e129f5cbaf3a8d765
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1040584
  timestamp: 1745955875845
- conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
  sha256: 202af1de83b585d36445dc1fda94266697341994d1a3328fabde4989e1b3d07a
  md5: d0d408b1f18883a944376da5cf8101ea
  depends:
  - ptyprocess >=0.5
  - python >=3.9
  license: ISC
  purls:
  - pkg:pypi/pexpect?source=hash-mapping
  size: 53561
  timestamp: 1733302019362
- conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
  sha256: e2ac3d66c367dada209fc6da43e645672364b9fd5f9d28b9f016e24b81af475b
  md5: 11a9d1d09a3615fc07c3faf79bc0b943
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pickleshare?source=hash-mapping
  size: 11748
  timestamp: 1733327448200
- conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.3.0-py311h3df08e7_1.conda
  sha256: 26b77626cdbc21c376ab0f7cb5e38a3fdc9cf184de30791b64972d2775e536cf
  md5: a36332b6f98697911d5760060f69ec87
  depends:
  - __glibc >=2.17,<3.0.a0
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=14
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  purls:
  - pkg:pypi/pillow?source=hash-mapping
  size: 42429659
  timestamp: 1756853546179
- conda: https://conda.anaconda.org/conda-forge/osx-64/pillow-11.3.0-py311h0d39b4b_1.conda
  sha256: 81c5c8e10eb2f42ee26fc1b795620b7462a3c8e9671b1d3a17b8d58cdd7bd89b
  md5: 3fefb6054f6a6f70d8adcec27a0f9b64
  depends:
  - __osx >=10.13
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  purls:
  - pkg:pypi/pillow?source=hash-mapping
  size: 42013280
  timestamp: 1756853759933
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pillow-11.3.0-py311h3f9ac88_1.conda
  sha256: 2b64066ad9b9660db1c5bc5347a01cce41384d6a4e7f698c6d1dcde067137db4
  md5: 5022d1df0b5861946b76dc55a6c48b4a
  depends:
  - __osx >=11.0
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  purls:
  - pkg:pypi/pillow?source=hash-mapping
  size: 41980810
  timestamp: 1756853923647
- conda: https://conda.anaconda.org/conda-forge/win-64/pillow-11.3.0-py311h0f9b5fc_1.conda
  sha256: 521239632ff6440bbd8b86c5d34b3d7e45dc2bb421e5da8d2b33c7da5f1879b4
  md5: 73cc32befbc5116e3e5d064b97bebd1c
  depends:
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - tk >=8.6.13,<8.7.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: HPND
  purls:
  - pkg:pypi/pillow?source=hash-mapping
  size: 42769802
  timestamp: 1756854011857
- conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
  sha256: ec9ed3cef137679f3e3a68e286c6efd52144684e1be0b05004d9699882dadcdd
  md5: dfce4b2af4bfe90cdcaf56ca0b28ddf5
  depends:
  - python >=3.9,<3.13.0a0
  - setuptools
  - wheel
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pip?source=hash-mapping
  size: 1177168
  timestamp: 1753924973872
- conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.4-h54a6638_1.conda
  sha256: 43d37bc9ca3b257c5dd7bf76a8426addbdec381f6786ff441dc90b1a49143b6a
  md5: c01af13bdc553d1a8fbfff6e8db075f0
  depends:
  - libgcc >=14
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  license: MIT
  license_family: MIT
  purls: []
  size: 450960
  timestamp: 1754665235234
- conda: https://conda.anaconda.org/conda-forge/win-64/pixman-0.46.4-h5112557_1.conda
  sha256: 246fce4706b3f8b247a7d6142ba8d732c95263d3c96e212b9d63d6a4ab4aff35
  md5: 08c8fa3b419df480d985e304f7884d35
  depends:
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  license: MIT
  license_family: MIT
  purls: []
  size: 542795
  timestamp: 1754665193489
- conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.4.0-pyhcf101f3_0.conda
  sha256: dfe0fa6e351d2b0cef95ac1a1533d4f960d3992f9e0f82aeb5ec3623a699896b
  md5: cc9d9a3929503785403dbfad9f707145
  depends:
  - python >=3.10
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/platformdirs?source=compressed-mapping
  size: 23653
  timestamp: 1756227402815
- conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
  sha256: 454e2c0ef14accc888dd2cd2e8adb8c6a3a607d2d3c2f93962698b5718e6176d
  md5: c64b77ccab10b822722904d889fa83b5
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/prometheus-client?source=hash-mapping
  size: 52641
  timestamp: 1748896836631
- conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.52-pyha770c72_0.conda
  sha256: 4817651a276016f3838957bfdf963386438c70761e9faec7749d411635979bae
  md5: edb16f14d920fb3faf17f5ce582942d6
  depends:
  - python >=3.10
  - wcwidth
  constrains:
  - prompt_toolkit 3.0.52
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/prompt-toolkit?source=hash-mapping
  size: 273927
  timestamp: 1756321848365
- conda: https://conda.anaconda.org/conda-forge/noarch/prompt_toolkit-3.0.52-hd8ed1ab_0.conda
  sha256: e79922a360d7e620df978417dd033e66226e809961c3e659a193f978a75a9b0b
  md5: 6d034d3a6093adbba7b24cb69c8c621e
  depends:
  - prompt-toolkit >=3.0.52,<3.0.53.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 7212
  timestamp: 1756321849562
- conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py311h49ec1c0_1.conda
  sha256: 729720d777b14329af411220fd305f78e8914356f963af0053420e1cf5e58a53
  md5: d30c3f3b089100634f93e97e5ee3aa85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/psutil?source=hash-mapping
  size: 483612
  timestamp: 1755851438911
- conda: https://conda.anaconda.org/conda-forge/osx-64/psutil-7.0.0-py311h13e5629_1.conda
  sha256: ce1b788a4bae81bd2246c7284d620152832b899394259f2f938755e13f3afc6c
  md5: d69888db150233f54b39919c12070de5
  depends:
  - __osx >=10.13
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/psutil?source=hash-mapping
  size: 490770
  timestamp: 1755851533700
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/psutil-7.0.0-py311h3696347_1.conda
  sha256: c21cd67c4037f232ba539f221839d1bcc7dbcc416d51f821fd319d91b5b61c3b
  md5: c449b450f0c81bc09e6a59a07adf95a1
  depends:
  - __osx >=11.0
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/psutil?source=hash-mapping
  size: 493127
  timestamp: 1755851546773
- conda: https://conda.anaconda.org/conda-forge/win-64/psutil-7.0.0-py311h3485c13_1.conda
  sha256: f48c2e47fda7259235f8abb55d219c419df3cc52e2e15ee9ee17da20b86393e5
  md5: cd66a378835a5da422201faac2c114c7
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/psutil?source=hash-mapping
  size: 499413
  timestamp: 1755851559633
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/osx-64/pthread-stubs-0.4-h00291cd_1002.conda
  sha256: 05944ca3445f31614f8c674c560bca02ff05cb51637a96f665cb2bbe496099e5
  md5: 8bcf980d2c6b17094961198284b8e862
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 8364
  timestamp: 1726802331537
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pthread-stubs-0.4-hd74edd7_1002.conda
  sha256: 8ed65e17fbb0ca944bfb8093b60086e3f9dd678c3448b5de212017394c247ee3
  md5: 415816daf82e0b23a736a069a75e9da7
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 8381
  timestamp: 1726802424786
- conda: https://conda.anaconda.org/conda-forge/win-64/pthread-stubs-0.4-h0e40799_1002.conda
  sha256: 7e446bafb4d692792310ed022fe284e848c6a868c861655a92435af7368bae7b
  md5: 3c8f2573569bb816483e5cf57efbbe29
  depends:
  - libgcc >=13
  - libwinpthread >=12.0.0.r4.gg4f2fc60ca
  - ucrt >=10.0.20348.0
  license: MIT
  license_family: MIT
  purls: []
  size: 9389
  timestamp: 1726802555076
- conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
  sha256: a7713dfe30faf17508ec359e0bc7e0983f5d94682492469bd462cdaae9c64d83
  md5: 7d9daffbb8d8e0af0f769dbbcd173a54
  depends:
  - python >=3.9
  license: ISC
  purls:
  - pkg:pypi/ptyprocess?source=hash-mapping
  size: 19457
  timestamp: 1733302371990
- conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
  sha256: 71bd24600d14bb171a6321d523486f6a06f855e75e547fa0cb2a0953b02047f0
  md5: 3bfdfb8dbcdc4af1ae3f9a8eb3948f04
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pure-eval?source=hash-mapping
  size: 16668
  timestamp: 1733569518868
- conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
  sha256: 79db7928d13fab2d892592223d7570f5061c192f27b9febd1a418427b719acc6
  md5: 12c566707c80111f9799308d9e265aef
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pycparser?source=hash-mapping
  size: 110100
  timestamp: 1733195786147
- pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
  name: pydantic
  version: 2.11.7
  sha256: dde5df002701f6de26248661f6835bbe296a47bf73990135c7d07ce741b9623b
  requires_dist:
  - annotated-types>=0.6.0
  - pydantic-core==2.33.2
  - typing-extensions>=4.12.2
  - typing-inspection>=0.4.0
  - email-validator>=2.0.0 ; extra == 'email'
  - tzdata ; python_full_version >= '3.9' and sys_platform == 'win32' and extra == 'timezone'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/24/2f/3cfa7244ae292dd850989f328722d2aef313f74ffc471184dc509e1e4e5a/pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: e799c050df38a639db758c617ec771fd8fb7a5f8eaaa4b27b101f266b216a246
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/3f/8d/71db63483d518cbbf290261a1fc2839d17ff89fce7089e08cad07ccfce67/pydantic_core-2.33.2-cp311-cp311-macosx_10_12_x86_64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: 4c5b0a576fb381edd6d27f0a85915c6daf2f8138dc5c267a57c08a62900758c7
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/47/bc/cd720e078576bdb8255d5032c5d63ee5c0bf4b7173dd955185a1d658c456/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: 881b21b5549499972441da4758d662aeea93f1923f953e9cbaff14b8b9565aef
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/fe/1b/25b7cccd4519c0b23c2dd636ad39d381abf113085ce4f7bec2b0dc755eb1/pydantic_core-2.33.2-cp311-cp311-win_amd64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: 1e063337ef9e9820c77acc768546325ebe04ee38b08703244c1309cccc4f1bab
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
  sha256: 5577623b9f6685ece2697c6eb7511b4c9ac5fb607c9babc2646c811b428fd46a
  md5: 6b6ece66ebcae2d5f326c77ef2c5a066
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pygments?source=hash-mapping
  size: 889287
  timestamp: 1750615908735
- conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-core-11.1-py311h2f44256_1.conda
  sha256: d0800d251981e3d124bd960e799dd0c56e8b16cd1c6fb99a32990d62f492d754
  md5: 840b246b6b86c37321e79ca56518273f
  depends:
  - __osx >=10.13
  - libffi >=3.4.6,<3.5.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - setuptools
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyobjc-core?source=hash-mapping
  size: 486295
  timestamp: 1756813198835
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-core-11.1-py311hf0763de_1.conda
  sha256: c5cbb88710d690ae2f571037708026abb8f15de1a4d764e4b825aaad6c4549c8
  md5: 8c42827190081e9c29a1007454890067
  depends:
  - __osx >=11.0
  - libffi >=3.4.6,<3.5.0a0
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  - setuptools
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyobjc-core?source=hash-mapping
  size: 477833
  timestamp: 1756813465248
- conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-framework-cocoa-11.1-py311hbc8e8a3_1.conda
  sha256: 71394c6e3f77898e856dab863f283e92f65b03e02e12fcf228bd3715a3459431
  md5: ab2c871073a077d149d6da6d03d10e44
  depends:
  - __osx >=10.13
  - libffi >=3.4.6,<3.5.0a0
  - pyobjc-core 11.1.*
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyobjc-framework-cocoa?source=hash-mapping
  size: 385837
  timestamp: 1756824131805
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-framework-cocoa-11.1-py311hc5b188e_1.conda
  sha256: 62db0aa2d3a23ae2ca2c28f2c462e63c0911903169c72a139a9e659e3ed02b19
  md5: ee9a511c6ffa04ac806bd2987b2b093d
  depends:
  - __osx >=11.0
  - libffi >=3.4.6,<3.5.0a0
  - pyobjc-core 11.1.*
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyobjc-framework-cocoa?source=hash-mapping
  size: 387176
  timestamp: 1756824223349
- conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.4-pyhcf101f3_0.conda
  sha256: c3260cf948da6345770d75ae559d716e557580eddcd19623676931d172346969
  md5: bf1f1292fc78307956289707e85cb1bf
  depends:
  - python >=3.10
  - python
  license: MIT
  purls:
  - pkg:pypi/pyparsing?source=compressed-mapping
  size: 104029
  timestamp: 1757767060575
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyside6-6.9.2-py311h72d58bf_1.conda
  sha256: 9540cbc6de195e8fb49ea4ef39567f11982580f007914617e1ab482193db114c
  md5: 4d8a5ee88cbf101a97b129eec7042af9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libclang13 >=21.1.0
  - libegl >=1.7.0,<2.0a0
  - libgcc >=14
  - libgl >=1.7.0,<2.0a0
  - libopengl >=1.7.0,<2.0a0
  - libstdcxx >=14
  - libxml2 >=2.13.8,<2.14.0a0
  - libxslt >=1.1.43,<2.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - qt6-main 6.9.2.*
  - qt6-main >=6.9.2,<6.10.0a0
  license: LGPL-3.0-only
  license_family: LGPL
  purls:
  - pkg:pypi/pyside6?source=hash-mapping
  - pkg:pypi/shiboken6?source=hash-mapping
  size: 10150360
  timestamp: 1756675160062
- conda: https://conda.anaconda.org/conda-forge/win-64/pyside6-6.9.2-py311h5d1a980_1.conda
  sha256: 8d6f5411eea073cf77daf335d904f64701abc3e8086b1f540c2b24ea8f342276
  md5: 372dcf53b700d63d76f46dab66bf41ed
  depends:
  - libclang13 >=21.1.0
  - libxml2 >=2.13.8,<2.14.0a0
  - libxslt >=1.1.43,<2.0a0
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - qt6-main 6.9.2.*
  - qt6-main >=6.9.2,<6.10.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: LGPL-3.0-only
  license_family: LGPL
  purls:
  - pkg:pypi/pyside6?source=hash-mapping
  - pkg:pypi/shiboken6?source=hash-mapping
  size: 8918376
  timestamp: 1756674096337
- conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyh09c184e_7.conda
  sha256: d016e04b0e12063fbee4a2d5fbb9b39a8d191b5a0042f0b8459188aedeabb0ca
  md5: e2fd202833c4a981ce8a65974fe4abd1
  depends:
  - __win
  - python >=3.9
  - win_inet_pton
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pysocks?source=hash-mapping
  size: 21784
  timestamp: 1733217448189
- conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
  sha256: ba3b032fa52709ce0d9fd388f63d330a026754587a2f461117cac9ab73d8d0d8
  md5: 461219d1a5bd61342293efa2c0c90eac
  depends:
  - __unix
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pysocks?source=hash-mapping
  size: 21085
  timestamp: 1733217331982
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.11.13-h9e4cc4f_0_cpython.conda
  sha256: 9979a7d4621049388892489267139f1aa629b10c26601ba5dce96afc2b1551d4
  md5: 8c399445b6dc73eab839659e6c7b5ad1
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  purls: []
  size: 30629559
  timestamp: 1749050021812
- conda: https://conda.anaconda.org/conda-forge/osx-64/python-3.11.13-h9ccd52b_0_cpython.conda
  sha256: d8e15db837c10242658979bc475298059bd6615524f2f71365ab8e54fbfea43c
  md5: 6e28c31688c6f1fdea3dc3d48d33e1c0
  depends:
  - __osx >=10.13
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  purls: []
  size: 15423460
  timestamp: 1749049420299
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.11.13-hc22306f_0_cpython.conda
  sha256: 2c966293ef9e97e66b55747c7a97bc95ba0311ac1cf0d04be4a51aafac60dcb1
  md5: 95facc4683b7b3b9cf8ae0ed10f30dce
  depends:
  - __osx >=11.0
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  purls: []
  size: 14573820
  timestamp: 1749048947732
- conda: https://conda.anaconda.org/conda-forge/win-64/python-3.11.13-h3f84c4b_0_cpython.conda
  sha256: 723dbca1384f30bd2070f77dd83eefd0e8d7e4dda96ac3332fbf8fe5573a8abb
  md5: bedbb6f7bb654839719cd528f9b298ad
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  purls: []
  size: 18242669
  timestamp: 1749048351218
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
  sha256: d6a17ece93bbd5139e02d2bd7dbfa80bee1a4261dced63f65f679121686bf664
  md5: 5b8d21249ff20967101ffa321cab24e8
  depends:
  - python >=3.9
  - six >=1.5
  - python
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/python-dateutil?source=hash-mapping
  size: 233310
  timestamp: 1751104122689
- conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.2-pyhe01879c_0.conda
  sha256: df9aa74e9e28e8d1309274648aac08ec447a92512c33f61a8de0afa9ce32ebe8
  md5: 23029aae904a2ba587daba708208012f
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/fastjsonschema?source=hash-mapping
  size: 244628
  timestamp: 1755304154927
- conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
  sha256: 4790787fe1f4e8da616edca4acf6a4f8ed4e7c6967aa31b920208fc8f95efcca
  md5: a61bf9ec79426938ff785eb69dbb1960
  depends:
  - python >=3.6
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/python-json-logger?source=hash-mapping
  size: 13383
  timestamp: 1677079727691
- conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
  sha256: e8392a8044d56ad017c08fec2b0eb10ae3d1235ac967d0aab8bd7b41c4a5eaf0
  md5: 88476ae6ebd24f39261e0854ac244f33
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/tzdata?source=hash-mapping
  size: 144160
  timestamp: 1742745254292
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
  build_number: 8
  sha256: fddf123692aa4b1fc48f0471e346400d9852d96eeed77dbfdd746fa50a8ff894
  md5: 8fcb6b0e2161850556231336dae58358
  constrains:
  - python 3.11.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 7003
  timestamp: 1752805919375
- conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
  sha256: 8d2a8bf110cc1fc3df6904091dead158ba3e614d8402a83e51ed3a8aa93cdeb0
  md5: bc8e3267d44011051f2eb14d22fb0960
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pytz?source=hash-mapping
  size: 189015
  timestamp: 1742920947249
- conda: https://conda.anaconda.org/conda-forge/win-64/pywin32-311-py311hefeebc8_1.conda
  sha256: e3ef7e0cc53111ab81b8a9dd3eabc1374d7420d4c9fce3c8631e73310203ad55
  md5: c1cfe9f5d8e278cc4d2d4c7b0126634d
  depends:
  - python
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - python_abi 3.11.* *_cp311
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/pywin32?source=hash-mapping
  size: 6729388
  timestamp: 1756487145061
- conda: https://conda.anaconda.org/conda-forge/win-64/pywinpty-2.0.15-py311hda3d55a_0.conda
  sha256: fbf3e3f2d5596e755bd4b83b5007fa629b184349781f46e137a4e80b6754c7c0
  md5: 8a142e0fcd43513c2e876d97ba98c0fa
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  - winpty
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pywinpty?source=hash-mapping
  size: 217009
  timestamp: 1738661736085
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py311h2dc5d0c_2.conda
  sha256: d107ad62ed5c62764fba9400f2c423d89adf917d687c7f2e56c3bfed605fb5b3
  md5: 014417753f948da1f70d132b2de573be
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyyaml?source=hash-mapping
  size: 213136
  timestamp: 1737454846598
- conda: https://conda.anaconda.org/conda-forge/osx-64/pyyaml-6.0.2-py311ha3cf9ac_2.conda
  sha256: 4855c51eedcde05f3d9666a0766050c7cbdff29b150d63c1adc4071637ba61d7
  md5: f49b0da3b1e172263f4f1e2f261a490d
  depends:
  - __osx >=10.13
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyyaml?source=hash-mapping
  size: 197287
  timestamp: 1737454852180
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyyaml-6.0.2-py311h4921393_2.conda
  sha256: 2af6006c9f692742181f4aa2e0656eb112981ccb0b420b899d3dd42c881bd72f
  md5: 250b2ee8777221153fd2de9c279a7efa
  depends:
  - __osx >=11.0
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyyaml?source=hash-mapping
  size: 196951
  timestamp: 1737454935552
- conda: https://conda.anaconda.org/conda-forge/win-64/pyyaml-6.0.2-py311h5082efb_2.conda
  sha256: 6095e1d58c666f6a06c55338df09485eac34c76e43d92121d5786794e195aa4d
  md5: e474ba674d780f0fa3b979ae9e81ba91
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyyaml?source=hash-mapping
  size: 187430
  timestamp: 1737454904007
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.1.0-py311h2315fbb_0.conda
  sha256: 719104f31c414166a20281c973b6e29d1a2ab35e7930327368949895b8bc5629
  md5: 6c87a0f4566469af3585b11d89163fd7
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=14
  - libgcc >=14
  - zeromq >=4.3.5,<4.4.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pyzmq?source=hash-mapping
  size: 386618
  timestamp: 1757387012835
- conda: https://conda.anaconda.org/conda-forge/osx-64/pyzmq-27.1.0-py311h0ab6910_0.conda
  sha256: a0200b5e781c22a5d7b64fd0edf5e9ab881772f7a15a6bc2359db8d0ac437bb3
  md5: 840bdfbb93e35d650205af10883ff8a0
  depends:
  - python
  - libcxx >=19
  - __osx >=10.13
  - zeromq >=4.3.5,<4.4.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pyzmq?source=hash-mapping
  size: 362304
  timestamp: 1757387126324
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.1.0-py311h13abfa4_0.conda
  sha256: 5a213744d267241e23f849c7671dc97eb98d7789fb559bf5d423ae1884294c7e
  md5: 0d16883d4ab2d3fcb38460d018d6762f
  depends:
  - python
  - __osx >=11.0
  - libcxx >=19
  - python 3.11.* *_cpython
  - zeromq >=4.3.5,<4.4.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pyzmq?source=hash-mapping
  size: 359600
  timestamp: 1757387159663
- conda: https://conda.anaconda.org/conda-forge/win-64/pyzmq-27.1.0-py311hb77b9c8_0.conda
  sha256: 1f146a62329093139fbe7fc109b595f19ca2b44beb921d0e1c6e61d2cb5ebef1
  md5: 96460f14570e237d27b475ef8238fdf3
  depends:
  - python
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - zeromq >=4.3.5,<4.3.6.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pyzmq?source=hash-mapping
  size: 363690
  timestamp: 1757387035331
- conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
  sha256: 776363493bad83308ba30bcb88c2552632581b143e8ee25b1982c8c743e73abc
  md5: 353823361b1d27eb3960efb076dfcaf6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LicenseRef-Qhull
  purls: []
  size: 552937
  timestamp: 1720813982144
- conda: https://conda.anaconda.org/conda-forge/osx-64/qhull-2020.2-h3c5361c_5.conda
  sha256: 79d804fa6af9c750e8b09482559814ae18cd8df549ecb80a4873537a5a31e06e
  md5: dd1ea9ff27c93db7c01a7b7656bd4ad4
  depends:
  - __osx >=10.13
  - libcxx >=16
  license: LicenseRef-Qhull
  purls: []
  size: 528122
  timestamp: 1720814002588
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/qhull-2020.2-h420ef59_5.conda
  sha256: 873ac689484262a51fd79bc6103c1a1bedbf524924d7f0088fb80703042805e4
  md5: 6483b1f59526e05d7d894e466b5b6924
  depends:
  - __osx >=11.0
  - libcxx >=16
  license: LicenseRef-Qhull
  purls: []
  size: 516376
  timestamp: 1720814307311
- conda: https://conda.anaconda.org/conda-forge/win-64/qhull-2020.2-hc790b64_5.conda
  sha256: 887d53486a37bd870da62b8fa2ebe3993f912ad04bd755e7ed7c47ced97cbaa8
  md5: 854fbdff64b572b5c0b470f334d34c11
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: LicenseRef-Qhull
  purls: []
  size: 1377020
  timestamp: 1720814433486
- conda: https://conda.anaconda.org/conda-forge/linux-64/qt6-main-6.9.2-h3fc9a0a_0.conda
  sha256: 70ca22551a307b7b23108dae31fc51dadac0742d44fc485bb7d3a865b4d47599
  md5: 70b5132b6e8a65198c2f9d5552c41126
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.14,<1.3.0a0
  - dbus >=1.16.2,<2.0a0
  - double-conversion >=3.3.1,<3.4.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - harfbuzz >=11.4.3
  - icu >=75.1,<76.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libclang-cpp20.1 >=20.1.8,<20.2.0a0
  - libclang13 >=20.1.8
  - libcups >=2.3.3,<2.4.0a0
  - libdrm >=2.4.125,<2.5.0a0
  - libegl >=1.7.0,<2.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=14
  - libgl >=1.7.0,<2.0a0
  - libglib >=2.84.3,<3.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libllvm20 >=20.1.8,<20.2.0a0
  - libpng >=1.6.50,<1.7.0a0
  - libpq >=17.6,<18.0a0
  - libsqlite >=3.50.4,<4.0a0
  - libstdcxx >=14
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libxkbcommon >=1.11.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.2,<4.0a0
  - pcre2 >=10.45,<10.46.0a0
  - wayland >=1.24.0,<2.0a0
  - xcb-util >=0.4.1,<0.5.0a0
  - xcb-util-cursor >=0.1.5,<0.2.0a0
  - xcb-util-image >=0.4.0,<0.5.0a0
  - xcb-util-keysyms >=0.4.1,<0.5.0a0
  - xcb-util-renderutil >=0.3.10,<0.4.0a0
  - xcb-util-wm >=0.4.2,<0.5.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.6,<2.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  - xorg-libxcomposite >=0.4.6,<1.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - xorg-libxdamage >=1.1.6,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrandr >=1.5.4,<2.0a0
  - xorg-libxtst >=1.2.5,<2.0a0
  - xorg-libxxf86vm >=1.1.6,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - qt 6.9.2
  license: LGPL-3.0-only
  license_family: LGPL
  purls: []
  size: 52566799
  timestamp: 1756296889250
- conda: https://conda.anaconda.org/conda-forge/win-64/qt6-main-6.9.2-h236c7cd_0.conda
  sha256: 5088ed0c6c769925a6df7d5a1a55fb7fc52278f327b986f45664453622fc98e2
  md5: 774ff6166c5f29c0c16e6c2bc43b485f
  depends:
  - double-conversion >=3.3.1,<3.4.0a0
  - harfbuzz >=11.4.3
  - icu >=75.1,<76.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libclang13 >=20.1.8
  - libglib >=2.84.3,<3.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libpng >=1.6.50,<1.7.0a0
  - libsqlite >=3.50.4,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.2,<4.0a0
  - pcre2 >=10.45,<10.46.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - qt 6.9.2
  license: LGPL-3.0-only
  license_family: LGPL
  purls: []
  size: 94567291
  timestamp: 1756296858553
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/osx-64/readline-8.2-h7cca4af_2.conda
  sha256: 53017e80453c4c1d97aaf78369040418dea14cf8f46a2fa999f31bd70b36c877
  md5: 342570f8e02f2f022147a7f841475784
  depends:
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 256712
  timestamp: 1740379577668
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
  sha256: 7db04684d3904f6151eff8673270922d31da1eea7fa73254d01c437f49702e34
  md5: 63ef3f6e6d6d5c589e64f11263dc5676
  depends:
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 252359
  timestamp: 1740379663071
- conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
  sha256: e20909f474a6cece176dfc0dc1addac265deb5fa92ea90e975fbca48085b20c3
  md5: 9140f1c09dd5489549c6a33931b943c7
  depends:
  - attrs >=22.2.0
  - python >=3.9
  - rpds-py >=0.7.0
  - typing_extensions >=4.4.0
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/referencing?source=hash-mapping
  size: 51668
  timestamp: 1737836872415
- conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.5-pyhd8ed1ab_0.conda
  sha256: 8dc54e94721e9ab545d7234aa5192b74102263d3e704e6d0c8aa7008f2da2a7b
  md5: db0c6b99149880c8ba515cf4abe93ee4
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.9
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/requests?source=hash-mapping
  size: 59263
  timestamp: 1755614348400
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
  sha256: 2e4372f600490a6e0b3bac60717278448e323cab1c0fecd5f43f7c56535a99c5
  md5: 36de09a8d3e5d5e6f4ee63af49e59706
  depends:
  - python >=3.9
  - six
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rfc3339-validator?source=hash-mapping
  size: 10209
  timestamp: 1733600040800
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
  sha256: 2a5b495a1de0f60f24d8a74578ebc23b24aa53279b1ad583755f223097c41c37
  md5: 912a71cc01012ee38e6b90ddd561e36f
  depends:
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rfc3986-validator?source=hash-mapping
  size: 7818
  timestamp: 1598024297745
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
  sha256: 70001ac24ee62058557783d9c5a7bbcfd97bd4911ef5440e3f7a576f9e43bc92
  md5: 7234f99325263a5af6d4cd195035e8f2
  depends:
  - python >=3.9
  - lark >=1.2.2
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rfc3987-syntax?source=hash-mapping
  size: 22913
  timestamp: 1752876729969
- conda: https://conda.anaconda.org/conda-forge/linux-64/rpds-py-0.27.1-py311h902ca64_1.conda
  sha256: d9bc1564949ede4abd32aea34cf1997d704b6091e547f255dc0168996f5d5ec8
  md5: 622c389c080689ba1575a0750eb0209d
  depends:
  - python
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - python_abi 3.11.* *_cp311
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rpds-py?source=hash-mapping
  size: 387057
  timestamp: 1756737832651
- conda: https://conda.anaconda.org/conda-forge/osx-64/rpds-py-0.27.1-py311hd3d88a1_1.conda
  sha256: 85357c87af076680c071a8ea843bea554d58694d011104b721cc13bbf9ad0e75
  md5: 4b9839b15de18289ee5289a6dbcb8a45
  depends:
  - python
  - __osx >=10.13
  - python_abi 3.11.* *_cp311
  constrains:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rpds-py?source=hash-mapping
  size: 376118
  timestamp: 1756737583772
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/rpds-py-0.27.1-py311h1c3fc1a_1.conda
  sha256: 95714a24265b6b4d4b218e303dcb075ba435826cb1d5927792ec94a8196c3e72
  md5: 5236ffaff99e6421aa4431b4c00ca47a
  depends:
  - python
  - python 3.11.* *_cpython
  - __osx >=11.0
  - python_abi 3.11.* *_cp311
  constrains:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rpds-py?source=hash-mapping
  size: 362213
  timestamp: 1756737586989
- conda: https://conda.anaconda.org/conda-forge/win-64/rpds-py-0.27.1-py311hf51aa87_1.conda
  sha256: e61607627213b70e7be73570e7ef5e2d36b583512def108aaf78a6ab16f0cdd9
  md5: 3c5b42969dae70e100154750d29d43cc
  depends:
  - python
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - python_abi 3.11.* *_cp311
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rpds-py?source=hash-mapping
  size: 247101
  timestamp: 1756737437304
- conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.16.2-py311h1e13796_0.conda
  sha256: e87176da9a36babfb2f65ca1143050b07581efea67368999808378c1c96163fd
  md5: 124834cd571d0174ad1c22701ab63199
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=14
  - numpy <2.6
  - numpy >=1.23,<3
  - numpy >=1.25.2
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  purls:
  - pkg:pypi/scipy?source=compressed-mapping
  size: 17289352
  timestamp: 1757682174416
- conda: https://conda.anaconda.org/conda-forge/osx-64/scipy-1.16.2-py311h32c7e5c_0.conda
  sha256: 8d8e69daa49c3c876fcacc31d31698d6d103e133c87c3d046fa67be4c0ad4a94
  md5: 8bf3fee43462b21388d04251f37159e6
  depends:
  - __osx >=10.13
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libcxx >=19
  - libgfortran
  - libgfortran5 >=14.3.0
  - libgfortran5 >=15.1.0
  - liblapack >=3.9.0,<4.0a0
  - numpy <2.6
  - numpy >=1.23,<3
  - numpy >=1.25.2
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  purls:
  - pkg:pypi/scipy?source=hash-mapping
  size: 15295538
  timestamp: 1757683511655
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/scipy-1.16.2-py311h2734c94_0.conda
  sha256: 972cd4e6379ad2ff96e36fd629c4dd0b2f32328f858848bfab8ad9a95c7f1d5e
  md5: dfe66d7dfba5ee328467bc10c4df4718
  depends:
  - __osx >=11.0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libcxx >=19
  - libgfortran
  - libgfortran5 >=14.3.0
  - libgfortran5 >=15.1.0
  - liblapack >=3.9.0,<4.0a0
  - numpy <2.6
  - numpy >=1.23,<3
  - numpy >=1.25.2
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  purls:
  - pkg:pypi/scipy?source=compressed-mapping
  size: 14047114
  timestamp: 1757684291857
- conda: https://conda.anaconda.org/conda-forge/win-64/scipy-1.16.2-py311h9a1c30b_0.conda
  sha256: 0bd54e04b152f4af55922b7ee792b42a1010c702d5b4d1ca47b062e67420e797
  md5: a5b6b853ae5a10a0d6225659d5e6019c
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  - numpy <2.6
  - numpy >=1.23,<3
  - numpy >=1.25.2
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: BSD-3-Clause
  purls:
  - pkg:pypi/scipy?source=hash-mapping
  size: 15306714
  timestamp: 1757683219896
- conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
  noarch: python
  sha256: ea29a69b14dd6be5cdeeaa551bf50d78cafeaf0351e271e358f9b820fcab4cb0
  md5: 62afb877ca2c2b4b6f9ecb37320085b6
  depends:
  - seaborn-base 0.13.2 pyhd8ed1ab_3
  - statsmodels >=0.12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6876
  timestamp: 1733730113224
- conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
  sha256: f209c9c18187570b85ec06283c72d64b8738f825b1b82178f194f4866877f8aa
  md5: fd96da444e81f9e6fcaac38590f3dd42
  depends:
  - matplotlib-base >=3.4,!=3.6.1
  - numpy >=1.20,!=1.24.0
  - pandas >=1.2
  - python >=3.9
  - scipy >=1.7
  constrains:
  - seaborn =0.13.2=*_3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/seaborn?source=hash-mapping
  size: 227843
  timestamp: 1733730112409
- conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh0d859eb_1.conda
  sha256: 00926652bbb8924e265caefdb1db100f86a479e8f1066efe395d5552dde54d02
  md5: 938c8de6b9de091997145b3bf25cdbf9
  depends:
  - __linux
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/send2trash?source=hash-mapping
  size: 22736
  timestamp: 1733322148326
- conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh31c8845_1.conda
  sha256: 5282eb5b462502c38df8cb37cd1542c5bbe26af2453a18a0a0602d084ca39f53
  md5: e67b1b1fa7a79ff9e8e326d0caf55854
  depends:
  - __osx
  - pyobjc-framework-cocoa
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/send2trash?source=hash-mapping
  size: 23100
  timestamp: 1733322309409
- conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh5737063_1.conda
  sha256: ba8b93df52e0d625177907852340d735026c81118ac197f61f1f5baea19071ad
  md5: e6a4e906051565caf5fdae5b0415b654
  depends:
  - __win
  - python >=3.9
  - pywin32
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/send2trash?source=hash-mapping
  size: 23359
  timestamp: 1733322590167
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
  sha256: 972560fcf9657058e3e1f97186cc94389144b46dbdf58c807ce62e83f977e863
  md5: 4de79c071274a53dcaf2a8c749d1499e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/setuptools?source=hash-mapping
  size: 748788
  timestamp: 1748804951958
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
  sha256: 458227f759d5e3fcec5d9b7acce54e10c9e1f4f4b7ec978f3bfd54ce4ee9853d
  md5: 3339e3b65d58accf4ca4fb8748ab16b3
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/six?source=hash-mapping
  size: 18455
  timestamp: 1753199211006
- conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
  sha256: c2248418c310bdd1719b186796ae50a8a77ce555228b6acd32768e2543a15012
  md5: bf7a226e58dfb8346c70df36065d86c9
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/sniffio?source=hash-mapping
  size: 15019
  timestamp: 1733244175724
- conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.8-pyhd8ed1ab_0.conda
  sha256: c978576cf9366ba576349b93be1cfd9311c00537622a2f9e14ba2b90c97cae9c
  md5: 18c019ccf43769d211f2cf78e9ad46c2
  depends:
  - python >=3.10
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/soupsieve?source=compressed-mapping
  size: 37803
  timestamp: 1756330614547
- conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
  sha256: 570da295d421661af487f1595045760526964f41471021056e993e73089e9c41
  md5: b1b505328da7a6b246787df4b5a49fbc
  depends:
  - asttokens
  - executing
  - pure_eval
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/stack-data?source=hash-mapping
  size: 26988
  timestamp: 1733569565672
- conda: https://conda.anaconda.org/conda-forge/linux-64/statsmodels-0.14.5-py311hb0beb2c_0.conda
  sha256: 766186eb4ff37a69479ec0695df9d7b775a8fd79b8ded727a1963cc1bedcfc80
  md5: 763f2b77d523c8662fa8a4fcabc4ef36
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - numpy <3,>=1.22.3
  - numpy >=1.23,<3
  - packaging >=21.3
  - pandas !=2.1.0,>=1.4
  - patsy >=0.5.6
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - scipy !=1.9.2,>=1.8
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/statsmodels?source=hash-mapping
  size: 12015823
  timestamp: 1751917868394
- conda: https://conda.anaconda.org/conda-forge/osx-64/statsmodels-0.14.5-py311ha2d3830_0.conda
  sha256: 6dcc2a61a73de340317241d70f9f47499c69a62e508b8b251af738bff631f1de
  md5: 2d429e456dd67796ed0e3114fb559f82
  depends:
  - __osx >=10.13
  - numpy <3,>=1.22.3
  - numpy >=1.23,<3
  - packaging >=21.3
  - pandas !=2.1.0,>=1.4
  - patsy >=0.5.6
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - scipy !=1.9.2,>=1.8
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/statsmodels?source=hash-mapping
  size: 11852156
  timestamp: 1751918004113
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/statsmodels-0.14.5-py311h9dc9093_0.conda
  sha256: a12928c2ed682227bbae9962519fb3316a9162db3c73a3afbb97138fdc3a7173
  md5: 0bb11b13609c9212051ea0c20a2acf2e
  depends:
  - __osx >=11.0
  - numpy <3,>=1.22.3
  - numpy >=1.23,<3
  - packaging >=21.3
  - pandas !=2.1.0,>=1.4
  - patsy >=0.5.6
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  - scipy !=1.9.2,>=1.8
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/statsmodels?source=hash-mapping
  size: 11795676
  timestamp: 1751917879126
- conda: https://conda.anaconda.org/conda-forge/win-64/statsmodels-0.14.5-py311h17033d2_0.conda
  sha256: e09d101d0044348e7a35f3a78f22bab0701a31059f02a63897cf87546e84e4f1
  md5: 9cf4eebc1ad82ee44efc89bdde60a431
  depends:
  - numpy <3,>=1.22.3
  - numpy >=1.23,<3
  - packaging >=21.3
  - pandas !=2.1.0,>=1.4
  - patsy >=0.5.6
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - scipy !=1.9.2,>=1.8
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/statsmodels?source=hash-mapping
  size: 11744906
  timestamp: 1751917965278
- conda: https://conda.anaconda.org/conda-forge/win-64/tbb-2021.13.0-h18a62a1_3.conda
  sha256: 30e82640a1ad9d9b5bee006da7e847566086f8fdb63d15b918794a7ef2df862c
  md5: 72226638648e494aaafde8155d50dab2
  depends:
  - libhwloc >=2.12.1,<2.12.2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 150266
  timestamp: 1755776172092
- conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh0d859eb_0.conda
  sha256: b300557c0382478cf661ddb520263508e4b3b5871b471410450ef2846e8c352c
  md5: efba281bbdae5f6b0a1d53c6d4a97c93
  depends:
  - __linux
  - ptyprocess
  - python >=3.8
  - tornado >=6.1.0
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/terminado?source=hash-mapping
  size: 22452
  timestamp: 1710262728753
- conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh31c8845_0.conda
  sha256: 4daae56fc8da17784578fbdd064f17e3b3076b394730a14119e571707568dc8a
  md5: 00b54981b923f5aefcd5e8547de056d5
  depends:
  - __osx
  - ptyprocess
  - python >=3.8
  - tornado >=6.1.0
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/terminado?source=hash-mapping
  size: 22717
  timestamp: 1710265922593
- conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh5737063_0.conda
  sha256: 8cb078291fd7882904e3de594d299c8de16dd3af7405787fce6919a385cfc238
  md5: 4abd500577430a942a995fd0d09b76a2
  depends:
  - __win
  - python >=3.8
  - pywinpty >=1.1.0
  - tornado >=6.1.0
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/terminado?source=hash-mapping
  size: 22883
  timestamp: 1710262943966
- conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
  sha256: cad582d6f978276522f84bd209a5ddac824742fe2d452af6acf900f8650a73a2
  md5: f1acf5fdefa8300de697982bcb1761c9
  depends:
  - python >=3.5
  - webencodings >=0.4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/tinycss2?source=hash-mapping
  size: 28285
  timestamp: 1729802975370
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/osx-64/tk-8.6.13-hf689a15_2.conda
  sha256: b24468006a96b71a5f4372205ea7ec4b399b0f2a543541e86f883de54cd623fc
  md5: 9864891a6946c2fe037c02fca7392ab4
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3259809
  timestamp: 1748387843735
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
  sha256: cb86c522576fa95c6db4c878849af0bccfd3264daf0cc40dd18e7f4a7bfced0e
  md5: 7362396c170252e7b7b0c8fb37fe9c78
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3125538
  timestamp: 1748388189063
- conda: https://conda.anaconda.org/conda-forge/win-64/tk-8.6.13-h2c6b04d_2.conda
  sha256: e3614b0eb4abcc70d98eae159db59d9b4059ed743ef402081151a948dce95896
  md5: ebd0e761de9aa879a51d22cc721bd095
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: TCL
  license_family: BSD
  purls: []
  size: 3466348
  timestamp: 1748388121356
- conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhe01879c_2.conda
  sha256: 040a5a05c487647c089ad5e05ad5aff5942830db2a4e656f1e300d73436436f1
  md5: 30a0a26c8abccf4b7991d590fe17c699
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/tomli?source=compressed-mapping
  size: 21238
  timestamp: 1753796677376
- conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.2-py311h49ec1c0_1.conda
  sha256: b1d686806d6b913e42aadb052b12d9cc91aae295640df3acfef645142fc33b3d
  md5: 18a98f4444036100d78b230c94453ff4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/tornado?source=hash-mapping
  size: 868049
  timestamp: 1756855060036
- conda: https://conda.anaconda.org/conda-forge/osx-64/tornado-6.5.2-py311h13e5629_1.conda
  sha256: 397226b6314aec2b076294816c941c1e9b7e3bbcf7a2e9dc9ba08f3ac10b0590
  md5: 06fd6a712ee16b3e7998e8ee2e7fc8b1
  depends:
  - __osx >=10.13
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/tornado?source=hash-mapping
  size: 869912
  timestamp: 1756855230920
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.2-py311h3696347_1.conda
  sha256: 4941963a1f0046b2813bfbe4c2ded15cb0b0048436fe62237d69467e8c0e1692
  md5: 25833dd6cb94341239aec42dd5370c33
  depends:
  - __osx >=11.0
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/tornado?source=hash-mapping
  size: 870380
  timestamp: 1756855179889
- conda: https://conda.anaconda.org/conda-forge/win-64/tornado-6.5.2-py311h3485c13_1.conda
  sha256: 87527996d1297442bbc432369a5791af740762c1dda642d52cd55d32d5577937
  md5: ec9179a7226659bd15d8085c8de15360
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/tornado?source=compressed-mapping
  size: 871179
  timestamp: 1756855104869
- pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
  name: tqdm
  version: 4.67.1
  sha256: 26445eca388f82e72884e0d580d5464cd801a3ea01e63e5601bdff9ba6a48de2
  requires_dist:
  - colorama ; sys_platform == 'win32'
  - pytest>=6 ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - pytest-timeout ; extra == 'dev'
  - pytest-asyncio>=0.24 ; extra == 'dev'
  - nbval ; extra == 'dev'
  - requests ; extra == 'discord'
  - slack-sdk ; extra == 'slack'
  - requests ; extra == 'telegram'
  - ipywidgets>=6 ; extra == 'notebook'
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
  sha256: f39a5620c6e8e9e98357507262a7869de2ae8cc07da8b7f84e517c9fd6c2b959
  md5: 019a7385be9af33791c989871317e1ed
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/traitlets?source=hash-mapping
  size: 110051
  timestamp: 1733367480074
- conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250822-pyhd8ed1ab_0.conda
  sha256: dfdf6e3dea87c873a86cfa47f7cba6ffb500bad576d083b3de6ad1b17e1a59c3
  md5: 5e9220c892fe069da8de2b9c63663319
  depends:
  - python >=3.10
  license: Apache-2.0 AND MIT
  purls:
  - pkg:pypi/types-python-dateutil?source=hash-mapping
  size: 24939
  timestamp: 1755865615651
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.15.0-h396c80c_0.conda
  sha256: 7c2df5721c742c2a47b2c8f960e718c930031663ac1174da67c1ed5999f7938c
  md5: edd329d7d3a4ab45dcf905899a7a6115
  depends:
  - typing_extensions ==4.15.0 pyhcf101f3_0
  license: PSF-2.0
  license_family: PSF
  purls: []
  size: 91383
  timestamp: 1756220668932
- pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
  name: typing-inspection
  version: 0.4.1
  sha256: 389055682238f53b04f7badcb49b989835495a96700ced5dab2d8feae4b26f51
  requires_dist:
  - typing-extensions>=4.12.0
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda
  sha256: 032271135bca55aeb156cee361c81350c6f3fb203f57d024d7e5a1fc9ef18731
  md5: 0caa1af407ecff61170c9437a808404d
  depends:
  - python >=3.10
  - python
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/typing-extensions?source=hash-mapping
  size: 51692
  timestamp: 1756220668932
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
  sha256: 3088d5d873411a56bf988eee774559335749aed6f6c28e07bf933256afb9eb6c
  md5: f6d7aa696c67756a650e91e15e88223c
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/typing-utils?source=hash-mapping
  size: 15183
  timestamp: 1733331395943
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  purls: []
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.26100.0-h57928b3_0.conda
  sha256: 3005729dce6f3d3f5ec91dfc49fc75a0095f9cd23bab49efb899657297ac91a5
  md5: 71b24316859acd00bdb8b38f5e2ce328
  constrains:
  - vc14_runtime >=14.29.30037
  - vs2015_runtime >=14.29.30037
  license: LicenseRef-MicrosoftWindowsSDK10
  purls: []
  size: 694692
  timestamp: 1756385147981
- conda: https://conda.anaconda.org/conda-forge/linux-64/unicodedata2-16.0.0-py311h49ec1c0_1.conda
  sha256: e2715a04632d75de539c1510238886ff1d6fc5b7e9e2ec240d8c11c175c1fffd
  md5: 3457bd5c93b085bec51cdab58fbd1882
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/unicodedata2?source=hash-mapping
  size: 405256
  timestamp: 1756494610217
- conda: https://conda.anaconda.org/conda-forge/osx-64/unicodedata2-16.0.0-py311h13e5629_1.conda
  sha256: cdd1276ff295078efb932f21305abda5392e8e43e7787050ea2d5ccbc04981ef
  md5: 4199c0fe9c425eddb08f5741fcb772c5
  depends:
  - __osx >=10.13
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/unicodedata2?source=hash-mapping
  size: 400393
  timestamp: 1756494700657
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/unicodedata2-16.0.0-py311h3696347_1.conda
  sha256: 800e8ed94f2d771b006891b5af4c4b510c4cab49e8966ac08297b68d904f0e15
  md5: 348a90d4b670542a7757e2415021bcf0
  depends:
  - __osx >=11.0
  - python >=3.11,<3.12.0a0
  - python >=3.11,<3.12.0a0 *_cpython
  - python_abi 3.11.* *_cp311
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/unicodedata2?source=hash-mapping
  size: 410696
  timestamp: 1756494744297
- conda: https://conda.anaconda.org/conda-forge/win-64/unicodedata2-16.0.0-py311h3485c13_1.conda
  sha256: d692506a8f0f9452c72d5b4b6d7d39bca7c383ab85749d82a77ad652ccbef940
  md5: 969071f934c7c811f014688e5ec4178f
  depends:
  - python >=3.11,<3.12.0a0
  - python_abi 3.11.* *_cp311
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/unicodedata2?source=hash-mapping
  size: 401855
  timestamp: 1756494775091
- conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
  sha256: e0eb6c8daf892b3056f08416a96d68b0a358b7c46b99c8a50481b22631a4dfc0
  md5: e7cb0f5745e4c5035a460248334af7eb
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/uri-template?source=hash-mapping
  size: 23990
  timestamp: 1733323714454
- conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
  sha256: 4fb9789154bd666ca74e428d973df81087a697dbb987775bc3198d2215f240f8
  md5: 436c165519e140cb08d246a4472a9d6a
  depends:
  - brotli-python >=1.0.9
  - h2 >=4,<5
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.9
  - zstandard >=0.18.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/urllib3?source=hash-mapping
  size: 101735
  timestamp: 1750271478254
- conda: https://conda.anaconda.org/conda-forge/win-64/vc-14.3-h41ae7f8_31.conda
  sha256: cb357591d069a1e6cb74199a8a43a7e3611f72a6caed9faa49dbb3d7a0a98e0b
  md5: 28f4ca1e0337d0f27afb8602663c5723
  depends:
  - vc14_runtime >=14.44.35208
  track_features:
  - vc14
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 18249
  timestamp: 1753739241465
- conda: https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.44.35208-h818238b_31.conda
  sha256: af4b4b354b87a9a8d05b8064ff1ea0b47083274f7c30b4eb96bc2312c9b5f08f
  md5: 603e41da40a765fd47995faa021da946
  depends:
  - ucrt >=10.0.20348.0
  - vcomp14 14.44.35208 h818238b_31
  constrains:
  - vs2015_runtime 14.44.35208.* *_31
  license: LicenseRef-MicrosoftVisualCpp2015-2022Runtime
  license_family: Proprietary
  purls: []
  size: 682424
  timestamp: 1753739239305
- conda: https://conda.anaconda.org/conda-forge/win-64/vcomp14-14.44.35208-h818238b_31.conda
  sha256: 67b317b64f47635415776718d25170a9a6f9a1218c0f5a6202bfd687e07b6ea4
  md5: a6b1d5c1fc3cb89f88f7179ee6a9afe3
  depends:
  - ucrt >=10.0.20348.0
  constrains:
  - vs2015_runtime 14.44.35208.* *_31
  license: LicenseRef-MicrosoftVisualCpp2015-2022Runtime
  license_family: Proprietary
  purls: []
  size: 113963
  timestamp: 1753739198723
- conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.24.0-h3e06ad9_0.conda
  sha256: ba673427dcd480cfa9bbc262fd04a9b1ad2ed59a159bd8f7e750d4c52282f34c
  md5: 0f2ca7906bf166247d1d760c3422cb8a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 330474
  timestamp: 1751817998141
- conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
  sha256: f21e63e8f7346f9074fd00ca3b079bd3d2fa4d71f1f89d5b6934bf31446dc2a5
  md5: b68980f2495d096e71c7fd9d7ccf63e6
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wcwidth?source=hash-mapping
  size: 32581
  timestamp: 1733231433877
- conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
  sha256: 08315dc2e61766a39219b2d82685fc25a56b2817acf84d5b390176080eaacf99
  md5: b49f7b291e15494aafb0a7d74806f337
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/webcolors?source=hash-mapping
  size: 18431
  timestamp: 1733359823938
- conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
  sha256: 19ff205e138bb056a46f9e3839935a2e60bd1cf01c8241a5e172a422fed4f9c6
  md5: 2841eb5bfc75ce15e9a0054b98dcd64d
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/webencodings?source=hash-mapping
  size: 15496
  timestamp: 1733236131358
- conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
  sha256: 1dd84764424ffc82030c19ad70607e6f9e3b9cb8e633970766d697185652053e
  md5: 84f8f77f0a9c6ef401ee96611745da8f
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/websocket-client?source=hash-mapping
  size: 46718
  timestamp: 1733157432924
- conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
  sha256: 1b34021e815ff89a4d902d879c3bd2040bc1bd6169b32e9427497fa05c55f1ce
  md5: 75cb7132eb58d97896e173ef12ac9986
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wheel?source=hash-mapping
  size: 62931
  timestamp: 1733130309598
- conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
  sha256: 7df3620c88343f2d960a58a81b79d4e4aa86ab870249e7165db7c3e2971a2664
  md5: 2f1f99b13b9d2a03570705030a0b3e7c
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/widgetsnbextension?source=hash-mapping
  size: 889285
  timestamp: 1744291155057
- conda: https://conda.anaconda.org/conda-forge/noarch/win_inet_pton-1.1.0-pyh7428d3b_8.conda
  sha256: 93807369ab91f230cf9e6e2a237eaa812492fe00face5b38068735858fba954f
  md5: 46e441ba871f524e2b067929da3051c2
  depends:
  - __win
  - python >=3.9
  license: LicenseRef-Public-Domain
  purls:
  - pkg:pypi/win-inet-pton?source=hash-mapping
  size: 9555
  timestamp: 1733130678956
- conda: https://conda.anaconda.org/conda-forge/win-64/winpty-0.4.3-4.tar.bz2
  sha256: 9df10c5b607dd30e05ba08cbd940009305c75db242476f4e845ea06008b0a283
  md5: 1cee351bf20b830d991dbe0bc8cd7dfe
  license: MIT
  license_family: MIT
  purls: []
  size: 1176306
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.1-h4f16b4b_2.conda
  sha256: ad8cab7e07e2af268449c2ce855cbb51f43f4664936eff679b1f3862e6e4b01d
  md5: fdc27cb255a7a2cc73b7919a968b48f0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 20772
  timestamp: 1750436796633
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-cursor-0.1.5-hb9d3cd8_0.conda
  sha256: c7b35db96f6e32a9e5346f97adc968ef2f33948e3d7084295baebc0e33abdd5b
  md5: eb44b3b6deb1cab08d72cb61686fe64c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.13
  - libxcb >=1.16,<2.0.0a0
  - xcb-util-image >=0.4.0,<0.5.0a0
  - xcb-util-renderutil >=0.3.10,<0.4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 20296
  timestamp: 1726125844850
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-hb711507_2.conda
  sha256: 94b12ff8b30260d9de4fd7a28cca12e028e572cbc504fd42aa2646ec4a5bded7
  md5: a0901183f08b6c7107aab109733a3c91
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  - xcb-util >=0.4.1,<0.5.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 24551
  timestamp: 1718880534789
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.1-hb711507_0.conda
  sha256: 546e3ee01e95a4c884b6401284bb22da449a2f4daf508d038fdfa0712fe4cc69
  md5: ad748ccca349aec3e91743e08b5e2b50
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 14314
  timestamp: 1718846569232
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.10-hb711507_0.conda
  sha256: 2d401dadc43855971ce008344a4b5bd804aca9487d8ebd83328592217daca3df
  md5: 0e0cbe0564d03a99afd5fd7b362feecd
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 16978
  timestamp: 1718848865819
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.2-hb711507_0.conda
  sha256: 31d44f297ad87a1e6510895740325a635dd204556aa7e079194a0034cdd7e66a
  md5: 608e0ef8256b81d04456e8d211eee3e8
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 51689
  timestamp: 1718844051451
- conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
  sha256: a5d4af601f71805ec67403406e147c48d6bad7aaeae92b0622b7e2396842d3fe
  md5: 397a013c2dc5145a70737871aaa87e98
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 392406
  timestamp: 1749375847832
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
  sha256: c12396aabb21244c212e488bbdc4abcdef0b7404b15761d9329f5a4a39113c4b
  md5: fb901ff28063514abb6046c9ec2c4a45
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 58628
  timestamp: 1734227592886
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
  sha256: 277841c43a39f738927145930ff963c5ce4c4dacf66637a3d95d802a64173250
  md5: 1c74ff8c35dcadf952a16f752ca5aa49
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 27590
  timestamp: 1741896361728
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
  sha256: 51909270b1a6c5474ed3978628b341b4d4472cd22610e5f22b506855a5e20f67
  md5: db038ce880f100acc74dba10302b5630
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 835896
  timestamp: 1741901112627
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxau-1.0.12-h6e16a3a_0.conda
  sha256: b4d2225135aa44e551576c4f3cf999b3252da6ffe7b92f0ad45bb44b887976fc
  md5: 4cf40e60b444d56512a64f39d12c20bd
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 13290
  timestamp: 1734229077182
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxau-1.0.12-h5505292_0.conda
  sha256: f33e6f013fc36ebc200f09ddead83468544cb5c353a3b50499b07b8c34e28a8d
  md5: 50901e0764b7701d8ed7343496f4f301
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 13593
  timestamp: 1734229104321
- conda: https://conda.anaconda.org/conda-forge/win-64/xorg-libxau-1.0.12-h0e40799_0.conda
  sha256: 047836241b2712aab1e29474a6f728647bff3ab57de2806b0bb0a6cf9a2d2634
  md5: 2ffbfae4548098297c033228256eb96e
  depends:
  - libgcc >=13
  - libwinpthread >=12.0.0.r4.gg4f2fc60ca
  - ucrt >=10.0.20348.0
  license: MIT
  license_family: MIT
  purls: []
  size: 108013
  timestamp: 1734229474049
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcomposite-0.4.6-hb9d3cd8_2.conda
  sha256: 753f73e990c33366a91fd42cc17a3d19bb9444b9ca5ff983605fa9e953baf57f
  md5: d3c295b50f092ab525ffe3c2aa4b7413
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 13603
  timestamp: 1727884600744
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
  sha256: 832f538ade441b1eee863c8c91af9e69b356cd3e9e1350fff4fe36cc573fc91a
  md5: 2ccd714aa2242315acaf0a67faea780b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 32533
  timestamp: 1730908305254
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdamage-1.1.6-hb9d3cd8_0.conda
  sha256: 43b9772fd6582bf401846642c4635c47a9b0e36ca08116b3ec3df36ab96e0ec0
  md5: b5fcc7172d22516e1f965490e65e33a4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 13217
  timestamp: 1727891438799
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxdmcp-1.1.5-h00291cd_0.conda
  sha256: bb4d1ef9cafef535494adf9296130b6193b3a44375883185b5167de03eb1ac7f
  md5: 9f438e1b6f4e73fd9e6d78bfe7c36743
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 18465
  timestamp: 1727794980957
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxdmcp-1.1.5-hd74edd7_0.conda
  sha256: 9939a166d780700d81023546759102b33fdc2c5f11ef09f5f66c77210fd334c8
  md5: 77c447f48cab5d3a15ac224edb86a968
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 18487
  timestamp: 1727795205022
- conda: https://conda.anaconda.org/conda-forge/win-64/xorg-libxdmcp-1.1.5-h0e40799_0.conda
  sha256: 9075f98dcaa8e9957e4a3d9d30db05c7578a536950a31c200854c5c34e1edb2c
  md5: 8393c0f7e7870b4eb45553326f81f0ff
  depends:
  - libgcc >=13
  - libwinpthread >=12.0.0.r4.gg4f2fc60ca
  - ucrt >=10.0.20348.0
  license: MIT
  license_family: MIT
  purls: []
  size: 69920
  timestamp: 1727795651979
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
  sha256: da5dc921c017c05f38a38bd75245017463104457b63a1ce633ed41f214159c14
  md5: febbab7d15033c913d53c7a2c102309d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 50060
  timestamp: 1727752228921
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
  sha256: 2fef37e660985794617716eb915865ce157004a4d567ed35ec16514960ae9271
  md5: 4bdb303603e9821baf5fe5fdff1dc8f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 19575
  timestamp: 1727794961233
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxi-1.8.2-hb9d3cd8_0.conda
  sha256: 1a724b47d98d7880f26da40e45f01728e7638e6ec69f35a3e11f92acd05f9e7a
  md5: 17dcc85db3c7886650b8908b183d6876
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 47179
  timestamp: 1727799254088
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrandr-1.5.4-hb9d3cd8_0.conda
  sha256: ac0f037e0791a620a69980914a77cb6bb40308e26db11698029d6708f5aa8e0d
  md5: 2de7f99d6581a4a7adbff607b5c278ca
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 29599
  timestamp: 1727794874300
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
  sha256: 044c7b3153c224c6cedd4484dd91b389d2d7fd9c776ad0f4a34f099b3389f4a1
  md5: 96d57aba173e878a2089d5638016dc5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 33005
  timestamp: 1734229037766
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxtst-1.2.5-hb9d3cd8_3.conda
  sha256: 752fdaac5d58ed863bbf685bb6f98092fe1a488ea8ebb7ed7b606ccfce08637a
  md5: 7bbe9a0cc0df0ac5f5a8ad6d6a11af2f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxi >=1.7.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 32808
  timestamp: 1727964811275
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxxf86vm-1.1.6-hb9d3cd8_0.conda
  sha256: 8a4e2ee642f884e6b78c20c0892b85dd9b2a6e64a6044e903297e616be6ca35b
  md5: 5efa5fa6243a622445fdfd72aee15efa
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 17819
  timestamp: 1734214575628
- conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h280c20c_3.conda
  sha256: 6d9ea2f731e284e9316d95fa61869fe7bbba33df7929f82693c121022810f4ad
  md5: a77f85f77be52ff59391544bfe73390a
  depends:
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  license: MIT
  license_family: MIT
  purls: []
  size: 85189
  timestamp: 1753484064210
- conda: https://conda.anaconda.org/conda-forge/osx-64/yaml-0.2.5-h4132b18_3.conda
  sha256: a335161bfa57b64e6794c3c354e7d49449b28b8d8a7c4ed02bf04c3f009953f9
  md5: a645bb90997d3fc2aea0adf6517059bd
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 79419
  timestamp: 1753484072608
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/yaml-0.2.5-h925e9cb_3.conda
  sha256: b03433b13d89f5567e828ea9f1a7d5c5d697bf374c28a4168d71e9464f5dafac
  md5: 78a0fe9e9c50d2c381e8ee47e3ea437d
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 83386
  timestamp: 1753484079473
- conda: https://conda.anaconda.org/conda-forge/win-64/yaml-0.2.5-h6a83c73_3.conda
  sha256: 80ee68c1e7683a35295232ea79bcc87279d31ffeda04a1665efdb43cbd50a309
  md5: 433699cba6602098ae8957a323da2664
  depends:
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  license: MIT
  license_family: MIT
  purls: []
  size: 63944
  timestamp: 1753484092156
- conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h387f397_9.conda
  sha256: 47cfe31255b91b4a6fa0e9dbaf26baa60ac97e033402dbc8b90ba5fee5ffe184
  md5: 8035e5b54c08429354d5d64027041cad
  depends:
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libsodium >=1.0.20,<1.0.21.0a0
  - krb5 >=1.21.3,<1.22.0a0
  license: MPL-2.0
  license_family: MOZILLA
  purls: []
  size: 310648
  timestamp: 1757370847287
- conda: https://conda.anaconda.org/conda-forge/osx-64/zeromq-4.3.5-h6c33b1e_9.conda
  sha256: 30aa5a2e9c7b8dbf6659a2ccd8b74a9994cdf6f87591fcc592970daa6e7d3f3c
  md5: d940d809c42fbf85b05814c3290660f5
  depends:
  - __osx >=10.13
  - libcxx >=19
  - libsodium >=1.0.20,<1.0.21.0a0
  - krb5 >=1.21.3,<1.22.0a0
  license: MPL-2.0
  license_family: MOZILLA
  purls: []
  size: 259628
  timestamp: 1757371000392
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-h888dc83_9.conda
  sha256: b6f9c130646e5971f6cad708e1eee278f5c7eea3ca97ec2fdd36e7abb764a7b8
  md5: 26f39dfe38a2a65437c29d69906a0f68
  depends:
  - __osx >=11.0
  - libcxx >=19
  - libsodium >=1.0.20,<1.0.21.0a0
  - krb5 >=1.21.3,<1.22.0a0
  license: MPL-2.0
  license_family: MOZILLA
  purls: []
  size: 244772
  timestamp: 1757371008525
- conda: https://conda.anaconda.org/conda-forge/win-64/zeromq-4.3.5-h5bddc39_9.conda
  sha256: 690cf749692c8ea556646d1a47b5824ad41b2f6dfd949e4cdb6c44a352fcb1aa
  md5: a6c8f8ee856f7c3c1576e14b86cd8038
  depends:
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - libsodium >=1.0.20,<1.0.21.0a0
  - krb5 >=1.21.3,<1.22.0a0
  license: MPL-2.0
  license_family: MOZILLA
  purls: []
  size: 265212
  timestamp: 1757370864284
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
  sha256: 7560d21e1b021fd40b65bfb72f67945a3fcb83d78ad7ccf37b8b3165ec3b68ad
  md5: df5e78d904988eb55042c0c97446079f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/zipp?source=hash-mapping
  size: 22963
  timestamp: 1749421737203
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.25.0-py311haee01d2_0.conda
  sha256: ed149760ea78e038e6424d8a327ea95da351727536c0e9abedccf5a61fc19932
  md5: 0fd242142b0691eb9311dc32c1d4ab76
  depends:
  - python
  - cffi >=1.11
  - zstd >=1.5.7,<1.5.8.0a0
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - python_abi 3.11.* *_cp311
  - zstd >=1.5.7,<1.6.0a0
  license: BSD-3-Clause
  purls:
  - pkg:pypi/zstandard?source=hash-mapping
  size: 466651
  timestamp: 1757930101225
- conda: https://conda.anaconda.org/conda-forge/osx-64/zstandard-0.25.0-py311h62e9434_0.conda
  sha256: be241ea3ca603d68654beeab4c991c225c9361378a107f72c2433ddfdff88132
  md5: 5425495af6b0b010230320d618022f20
  depends:
  - python
  - cffi >=1.11
  - zstd >=1.5.7,<1.5.8.0a0
  - __osx >=10.13
  - python_abi 3.11.* *_cp311
  - zstd >=1.5.7,<1.6.0a0
  license: BSD-3-Clause
  purls:
  - pkg:pypi/zstandard?source=hash-mapping
  size: 462903
  timestamp: 1757930157317
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstandard-0.25.0-py311h5bb9006_0.conda
  sha256: fb1443a1479a6d709b4af7a2cdcea3ef2a5e859378de19814086fa86ca6f934e
  md5: c7e0f1b714bd12d39899a4f0c296dd86
  depends:
  - python
  - cffi >=1.11
  - zstd >=1.5.7,<1.5.8.0a0
  - __osx >=11.0
  - python 3.11.* *_cpython
  - zstd >=1.5.7,<1.6.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  purls:
  - pkg:pypi/zstandard?source=hash-mapping
  size: 390089
  timestamp: 1757930124840
- conda: https://conda.anaconda.org/conda-forge/win-64/zstandard-0.25.0-py311hf893f09_0.conda
  sha256: 3b66d3cb738a9993e8432d1a03402d207128166c4ef0c928e712958e51aff325
  md5: d26077d20b4bba54f4c718ed1313805f
  depends:
  - python
  - cffi >=1.11
  - zstd >=1.5.7,<1.5.8.0a0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - zstd >=1.5.7,<1.6.0a0
  - python_abi 3.11.* *_cp311
  license: BSD-3-Clause
  purls:
  - pkg:pypi/zstandard?source=hash-mapping
  size: 375866
  timestamp: 1757930134099
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 567578
  timestamp: 1742433379869
- conda: https://conda.anaconda.org/conda-forge/osx-64/zstd-1.5.7-h8210216_2.conda
  sha256: c171c43d0c47eed45085112cb00c8c7d4f0caa5a32d47f2daca727e45fb98dca
  md5: cd60a4a5a8d6a476b30d8aa4bb49251a
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 485754
  timestamp: 1742433356230
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstd-1.5.7-h6491c7d_2.conda
  sha256: 0d02046f57f7a1a3feae3e9d1aa2113788311f3cf37a3244c71e61a93177ba67
  md5: e6f69c7bcccdefa417f056fa593b40f0
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 399979
  timestamp: 1742433432699
- conda: https://conda.anaconda.org/conda-forge/win-64/zstd-1.5.7-hbeecb71_2.conda
  sha256: bc64864377d809b904e877a98d0584f43836c9f2ef27d3d2a1421fa6eae7ca04
  md5: 21f56217d6125fb30c3c3f10c786d751
  depends:
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 354697
  timestamp: 1742433568506
