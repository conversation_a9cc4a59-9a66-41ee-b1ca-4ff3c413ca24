version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.anaconda.org/pytorch/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-35_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-35_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-35_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb03c661_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nodejs-24.4.1-heeeca48_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py311h2e04523_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.2-h26f9b46_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.11.13-h9e4cc4f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6f/12/e5e0282d673bb9746bacfb6e2dba8719989d3660cdb2ea79aee9a9651afb/anyio-4.10.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/48/1549795ba7742c948d2ad169c1c8cdbae65bc450d6cd753d124b17c8cd32/certifi-2025.8.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/81/5a/0e73541b6edd3f4aada586c24e50626c7815c561a7ba337d6a7eb0a915b4/jiter-0.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/bc/cd720e078576bdb8255d5032c5d63ee5c0bf4b7173dd955185a1d658c456/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/18/67/36e9267722cc04a6b9f15c7f3441c2363321a3ea07da7ae0c0707beb2a9c/typing_extensions-4.15.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      osx-64:
      - conda: https://conda.anaconda.org/conda-forge/osx-64/bzip2-1.0.8-hfdf4475_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/icu-75.1-h120a0e1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libblas-3.9.0-35_h7f60823_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcblas-3.9.0-35_hff6cab4_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcxx-21.1.0-h3d58e20_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libexpat-2.7.1-h21dd04a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libffi-3.4.6-h281671d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran-15.1.0-h5f6db21_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran5-15.1.0-hfa3c126_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblapack-3.9.0-35_h236ab99_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblzma-5.8.1-hd471939_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libopenblas-0.3.30-openmp_h83c2472_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libsqlite-3.50.4-h39a8b3b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libuv-1.51.0-h58003a5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libzlib-1.3.1-hd23fc13_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/llvm-openmp-21.1.0-hf4e0ed4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/ncurses-6.5-h0622a9a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/nodejs-24.4.1-h2e7699b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/numpy-2.3.2-py311h09fcace_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/openssl-3.5.2-h6e31bce_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/python-3.11.13-h9ccd52b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/readline-8.2-h7cca4af_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/tk-8.6.13-hf689a15_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6f/12/e5e0282d673bb9746bacfb6e2dba8719989d3660cdb2ea79aee9a9651afb/anyio-4.10.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/48/1549795ba7742c948d2ad169c1c8cdbae65bc450d6cd753d124b17c8cd32/certifi-2025.8.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/1b/dd/6cefc6bd68b1c3c979cecfa7029ab582b57690a31cd2f346c4d0ce7951b6/jiter-0.10.0-cp311-cp311-macosx_10_12_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3f/8d/71db63483d518cbbf290261a1fc2839d17ff89fce7089e08cad07ccfce67/pydantic_core-2.33.2-cp311-cp311-macosx_10_12_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/18/67/36e9267722cc04a6b9f15c7f3441c2363321a3ea07da7ae0c0707beb2a9c/typing_extensions-4.15.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      osx-arm64:
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-35_h10e41b3_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-35_hb3479ef_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-21.1.0-hf598326_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.1-hec049ff_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-15.1.0-hfdf1602_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-15.1.0-hb74de2c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-35_hc9a63f6_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_h60d53f8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.4-h4237e3c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libuv-1.51.0-h6caf38d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-21.1.0-hbb9b287_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/nodejs-24.4.1-hab9d20b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.2-py311h0856f98_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.2-he92f556_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.11.13-hc22306f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6f/12/e5e0282d673bb9746bacfb6e2dba8719989d3660cdb2ea79aee9a9651afb/anyio-4.10.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/48/1549795ba7742c948d2ad169c1c8cdbae65bc450d6cd753d124b17c8cd32/certifi-2025.8.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/be/cf/fc33f5159ce132be1d8dd57251a1ec7a631c7df4bd11e1cd198308c6ae32/jiter-0.10.0-cp311-cp311-macosx_11_0_arm64.whl
      - pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/24/2f/3cfa7244ae292dd850989f328722d2aef313f74ffc471184dc509e1e4e5a/pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/18/67/36e9267722cc04a6b9f15c7f3441c2363321a3ea07da7ae0c0707beb2a9c/typing_extensions-4.15.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      win-64:
      - conda: https://conda.anaconda.org/conda-forge/win-64/bzip2-1.0.8-h2466b09_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-h4c7d964_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libblas-3.9.0-35_h5709861_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libcblas-3.9.0-35_h2a3cdd5_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libexpat-2.7.1-hac47afa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libffi-3.4.6-h537db12_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libhwloc-2.12.1-default_h88281d1_1000.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libiconv-1.18-hc1393d2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/liblapack-3.9.0-35_hf9ab0e9_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/liblzma-5.8.1-h2466b09_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libsqlite-3.50.4-hf5d6505_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libwinpthread-12.0.0.r4.gg4f2fc60ca-h57928b3_9.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libxml2-2.13.8-h741aa76_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-1.3.1-h2466b09_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/llvm-openmp-20.1.8-hfa2b4ca_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/mkl-2024.2.2-h57928b3_16.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/nodejs-24.4.1-he453025_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/numpy-2.3.2-py311h80b3fa1_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/openssl-3.5.2-h725018a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/python-3.11.13-h3f84c4b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/tbb-2021.13.0-h18a62a1_3.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/tk-8.6.13-h2c6b04d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.26100.0-h57928b3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vc-14.3-h41ae7f8_31.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.44.35208-h818238b_31.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vcomp14-14.44.35208-h818238b_31.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6f/12/e5e0282d673bb9746bacfb6e2dba8719989d3660cdb2ea79aee9a9651afb/anyio-4.10.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/48/1549795ba7742c948d2ad169c1c8cdbae65bc450d6cd753d124b17c8cd32/certifi-2025.8.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/c9/d394706deb4c660137caf13e33d05a031d734eb99c051142e039d8ceb794/jiter-0.10.0-cp311-cp311-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fe/1b/25b7cccd4519c0b23c2dd636ad39d381abf113085ce4f7bec2b0dc755eb1/pydantic_core-2.33.2-cp311-cp311-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/18/67/36e9267722cc04a6b9f15c7f3441c2363321a3ea07da7ae0c0707beb2a9c/typing_extensions-4.15.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  purls: []
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23621
  timestamp: 1650670423406
- pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
  name: annotated-types
  version: 0.7.0
  sha256: 1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53
  requires_dist:
  - typing-extensions>=4.0.0 ; python_full_version < '3.9'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/6f/12/e5e0282d673bb9746bacfb6e2dba8719989d3660cdb2ea79aee9a9651afb/anyio-4.10.0-py3-none-any.whl
  name: anyio
  version: 4.10.0
  sha256: 60e474ac86736bbfd6f210f7a61218939c318f43f9972497381f1c5e930ed3d1
  requires_dist:
  - exceptiongroup>=1.0.2 ; python_full_version < '3.11'
  - idna>=2.8
  - sniffio>=1.1
  - typing-extensions>=4.5 ; python_full_version < '3.13'
  - trio>=0.26.1 ; extra == 'trio'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/osx-64/bzip2-1.0.8-hfdf4475_7.conda
  sha256: cad153608b81fb24fc8c509357daa9ae4e49dfc535b2cb49b91e23dbd68fc3c5
  md5: 7ed4301d437b59045be7e051a0308211
  depends:
  - __osx >=10.13
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 134188
  timestamp: 1720974491916
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
  sha256: adfa71f158cbd872a36394c56c3568e6034aa55c623634b37a4836bd036e6b91
  md5: fc6948412dbbbe9a4c9ddbbcfe0a79ab
  depends:
  - __osx >=11.0
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 122909
  timestamp: 1720974522888
- conda: https://conda.anaconda.org/conda-forge/win-64/bzip2-1.0.8-h2466b09_7.conda
  sha256: 35a5dad92e88fdd7fc405e864ec239486f4f31eec229e31686e61a140a8e573b
  md5: 276e7ffe9ffe39688abc665ef0f45596
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 54927
  timestamp: 1720974860185
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-h4c7d964_0.conda
  sha256: 3b82f62baad3fd33827b01b0426e8203a2786c8f452f633740868296bcbe8485
  md5: c9e0c0f82f6e63323827db462b40ede8
  depends:
  - __win
  license: ISC
  purls: []
  size: 154489
  timestamp: 1754210967212
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda
  sha256: 837b795a2bb39b75694ba910c13c15fa4998d4bb2a622c214a6a5174b2ae53d1
  md5: 74784ee3d225fc3dca89edb635b4e5cc
  depends:
  - __unix
  license: ISC
  purls: []
  size: 154402
  timestamp: 1754210968730
- pypi: https://files.pythonhosted.org/packages/e5/48/1549795ba7742c948d2ad169c1c8cdbae65bc450d6cd753d124b17c8cd32/certifi-2025.8.3-py3-none-any.whl
  name: certifi
  version: 2025.8.3
  sha256: f6c12493cfb1b06ba2ff328595af9350c65d6644968e5d3a2ffd78699af217a5
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl
  name: colorama
  version: 0.4.6
  sha256: 4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*'
- pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
  name: distro
  version: 1.9.0
  sha256: 7bffd925d65168f85027d8da9af6bddab658135b840670a223589bc0c8ef02b2
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
  name: h11
  version: 0.16.0
  sha256: 63cf8bbe7522de3bf65932fda1d9c2772064ffb3dae62d55932da54b31cb6c86
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
  name: httpcore
  version: 1.0.9
  sha256: 2d400746a40668fc9dec9810239072b40b4484b640a8c38fd654a024c7a1bf55
  requires_dist:
  - certifi
  - h11>=0.16
  - anyio>=4.0,<5.0 ; extra == 'asyncio'
  - h2>=3,<5 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - trio>=0.22.0,<1.0 ; extra == 'trio'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
  name: httpx
  version: 0.28.1
  sha256: d909fcccc110f8c7faf814ca82a9a4d816bc5a6dbfea25d6591d6985b8ba59ad
  requires_dist:
  - anyio
  - certifi
  - httpcore==1.*
  - idna
  - brotli ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - click==8.* ; extra == 'cli'
  - pygments==2.* ; extra == 'cli'
  - rich>=10,<14 ; extra == 'cli'
  - h2>=3,<5 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - zstandard>=0.18.0 ; extra == 'zstd'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  purls: []
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/osx-64/icu-75.1-h120a0e1_0.conda
  sha256: 2e64307532f482a0929412976c8450c719d558ba20c0962832132fd0d07ba7a7
  md5: d68d48a3060eb5abdc1cdc8e2a3a5966
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 11761697
  timestamp: 1720853679409
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
  sha256: 9ba12c93406f3df5ab0a43db8a4b4ef67a5871dfd401010fbe29b218b2cbe620
  md5: 5eb22c1d7b3fc4abb50d92d621583137
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 11857802
  timestamp: 1720853997952
- pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
  name: idna
  version: '3.10'
  sha256: 946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3
  requires_dist:
  - ruff>=0.6.2 ; extra == 'all'
  - mypy>=1.11.2 ; extra == 'all'
  - pytest>=8.3.2 ; extra == 'all'
  - flake8>=7.1.1 ; extra == 'all'
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/1b/dd/6cefc6bd68b1c3c979cecfa7029ab582b57690a31cd2f346c4d0ce7951b6/jiter-0.10.0-cp311-cp311-macosx_10_12_x86_64.whl
  name: jiter
  version: 0.10.0
  sha256: 3bebe0c558e19902c96e99217e0b8e8b17d570906e72ed8a87170bc290b1e978
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/81/5a/0e73541b6edd3f4aada586c24e50626c7815c561a7ba337d6a7eb0a915b4/jiter-0.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: jiter
  version: 0.10.0
  sha256: 4c440ea003ad10927a30521a9062ce10b5479592e8a70da27f21eeb457b4a9c5
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/be/cf/fc33f5159ce132be1d8dd57251a1ec7a631c7df4bd11e1cd198308c6ae32/jiter-0.10.0-cp311-cp311-macosx_11_0_arm64.whl
  name: jiter
  version: 0.10.0
  sha256: 558cc7e44fd8e507a236bee6a02fa17199ba752874400a0ca6cd6e2196cdb7dc
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/c2/c9/d394706deb4c660137caf13e33d05a031d734eb99c051142e039d8ceb794/jiter-0.10.0-cp311-cp311-win_amd64.whl
  name: jiter
  version: 0.10.0
  sha256: 9c9c1d5f10e18909e993f9641f12fe1c77b3e9b533ee94ffa970acc14ded3812
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
  sha256: 1a620f27d79217c1295049ba214c2f80372062fd251b569e9873d4a953d27554
  md5: 0be7c6e070c19105f966d3758448d018
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.44
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 676044
  timestamp: 1752032747103
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-35_h59b9bed_openblas.conda
  build_number: 35
  sha256: 83d0755acd486660532003bc2562223504b57732bc7e250985391ce335692cf7
  md5: eaf80af526daf5745295d9964c2bd3cf
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - blas 2.135   openblas
  - liblapack  3.9.0   35*_openblas
  - libcblas   3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  - mkl <2025
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 16937
  timestamp: 1757002815691
- conda: https://conda.anaconda.org/conda-forge/osx-64/libblas-3.9.0-35_h7f60823_openblas.conda
  build_number: 35
  sha256: 29db26b643bd1e73d5c658bdd0d21ab3c1caa7cfcc62d1d368b1c8e25105e1a0
  md5: 4479d770836307d0bac28281d00ccfa9
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - liblapack  3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  - libcblas   3.9.0   35*_openblas
  - blas 2.135   openblas
  - mkl <2025
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17151
  timestamp: 1757003585301
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-35_h10e41b3_openblas.conda
  build_number: 35
  sha256: 52ddab13634559d8fc9c7808c52200613bb3825c6e0708820f5744aa55324702
  md5: 0b59bf8bb0bc16b2c5bdae947a44e1f1
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - libcblas   3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  - liblapack  3.9.0   35*_openblas
  - mkl <2025
  - blas 2.135   openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17191
  timestamp: 1757003619794
- conda: https://conda.anaconda.org/conda-forge/win-64/libblas-3.9.0-35_h5709861_mkl.conda
  build_number: 35
  sha256: 4180e7ab27ed03ddf01d7e599002fcba1b32dcb68214ee25da823bac371ed362
  md5: 45d98af023f8b4a7640b1f713ce6b602
  depends:
  - mkl >=2024.2.2,<2025.0a0
  constrains:
  - blas 2.135   mkl
  - liblapack  3.9.0   35*_mkl
  - libcblas   3.9.0   35*_mkl
  - liblapacke 3.9.0   35*_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 66044
  timestamp: 1757003486248
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-35_he106b2a_openblas.conda
  build_number: 35
  sha256: 6f296f1567a7052c0f8b9527f74cfebc5418dbbae6dcdbae8659963f8ae7f48e
  md5: e62d58d32431dabed236c860dfa566ca
  depends:
  - libblas 3.9.0 35_h59b9bed_openblas
  constrains:
  - blas 2.135   openblas
  - liblapack  3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 16900
  timestamp: 1757002826333
- conda: https://conda.anaconda.org/conda-forge/osx-64/libcblas-3.9.0-35_hff6cab4_openblas.conda
  build_number: 35
  sha256: 1d4542fe690a7aa153dc7d8f691702812c179ed29d0a0e648f83830d7001456c
  md5: 096acef4d495c2454c3771034ce5d23a
  depends:
  - libblas 3.9.0 35_h7f60823_openblas
  constrains:
  - blas 2.135   openblas
  - liblapack  3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17132
  timestamp: 1757003606725
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-35_hb3479ef_openblas.conda
  build_number: 35
  sha256: dfd8dd4eea94894a01c1e4d9a409774ad2b71f77e713eb8c0dc62bb47abe2f0b
  md5: 7f8a6b52d39bde47c6f2d3ef96bd5e68
  depends:
  - libblas 3.9.0 35_h10e41b3_openblas
  constrains:
  - liblapacke 3.9.0   35*_openblas
  - blas 2.135   openblas
  - liblapack  3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17163
  timestamp: 1757003634172
- conda: https://conda.anaconda.org/conda-forge/win-64/libcblas-3.9.0-35_h2a3cdd5_mkl.conda
  build_number: 35
  sha256: 88939f6c1b5da75bd26ce663aa437e1224b26ee0dab5e60cecc77600975f397e
  md5: 9639091d266e92438582d0cc4cfc8350
  depends:
  - libblas 3.9.0 35_h5709861_mkl
  constrains:
  - blas 2.135   mkl
  - liblapack  3.9.0   35*_mkl
  - liblapacke 3.9.0   35*_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 66398
  timestamp: 1757003514529
- conda: https://conda.anaconda.org/conda-forge/osx-64/libcxx-21.1.0-h3d58e20_1.conda
  sha256: ff2c82c14232cc0ff8038b3d43dace4a792c05a9b01465448445ac52539dee40
  md5: d5bb255dcf8d208f30089a5969a0314b
  depends:
  - __osx >=10.13
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 572463
  timestamp: 1756698407882
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-21.1.0-hf598326_1.conda
  sha256: 58427116dc1b58b13b48163808daa46aacccc2c79d40000f8a3582938876fed7
  md5: 0fb2c0c9b1c1259bc7db75c1342b1d99
  depends:
  - __osx >=11.0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 568692
  timestamp: 1756698505599
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
  sha256: da2080da8f0288b95dd86765c801c6e166c4619b910b11f9a8446fb852438dc2
  md5: 4211416ecba1866fab0c6470986c22d6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 74811
  timestamp: 1752719572741
- conda: https://conda.anaconda.org/conda-forge/osx-64/libexpat-2.7.1-h21dd04a_0.conda
  sha256: 689862313571b62ee77ee01729dc093f2bf25a2f99415fcfe51d3a6cd31cce7b
  md5: 9fdeae0b7edda62e989557d645769515
  depends:
  - __osx >=10.13
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 72450
  timestamp: 1752719744781
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.1-hec049ff_0.conda
  sha256: 8fbb17a56f51e7113ed511c5787e0dec0d4b10ef9df921c4fd1cccca0458f648
  md5: b1ca5f21335782f71a8bd69bdc093f67
  depends:
  - __osx >=11.0
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 65971
  timestamp: 1752719657566
- conda: https://conda.anaconda.org/conda-forge/win-64/libexpat-2.7.1-hac47afa_0.conda
  sha256: 8432ca842bdf8073ccecf016ccc9140c41c7114dc4ec77ca754551c01f780845
  md5: 3608ffde260281fa641e70d6e34b1b96
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  purls: []
  size: 141322
  timestamp: 1752719767870
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/osx-64/libffi-3.4.6-h281671d_1.conda
  sha256: 6394b1bc67c64a21a5cc73d1736d1d4193a64515152e861785c44d2cfc49edf3
  md5: 4ca9ea59839a9ca8df84170fab4ceb41
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 51216
  timestamp: 1743434595269
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
  sha256: c6a530924a9b14e193ea9adfe92843de2a806d1b7dbfd341546ece9653129e60
  md5: c215a60c2935b517dcda8cad4705734d
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 39839
  timestamp: 1743434670405
- conda: https://conda.anaconda.org/conda-forge/win-64/libffi-3.4.6-h537db12_1.conda
  sha256: d3b0b8812eab553d3464bbd68204f007f1ebadf96ce30eb0cbc5159f72e353f5
  md5: 85d8fa5e55ed8f93f874b3b23ed54ec6
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: MIT
  license_family: MIT
  purls: []
  size: 44978
  timestamp: 1743435053850
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_5.conda
  sha256: 0caed73aac3966bfbf5710e06c728a24c6c138605121a3dacb2e03440e8baa6a
  md5: 264fbfba7fb20acf3b29cde153e345ce
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 15.1.0 h767d61c_5
  - libgcc-ng ==15.1.0=*_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 824191
  timestamp: 1757042543820
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_5.conda
  sha256: f54bb9c3be12b24be327f4c1afccc2969712e0b091cdfbd1d763fb3e61cda03f
  md5: 069afdf8ea72504e48d23ae1171d951c
  depends:
  - libgcc 15.1.0 h767d61c_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29187
  timestamp: 1757042549554
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_5.conda
  sha256: 4c1a526198d0d62441549fdfd668cc8e18e77609da1e545bdcc771dd8dc6a990
  md5: 0c91408b3dec0b97e8a3c694845bd63b
  depends:
  - libgfortran5 15.1.0 hcea5267_5
  constrains:
  - libgfortran-ng ==15.1.0=*_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29169
  timestamp: 1757042575979
- conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran-15.1.0-h5f6db21_1.conda
  sha256: 844500c9372d455f6ae538ffd3cdd7fda5f53d25a2a6b3ba33060a302c37bc3e
  md5: 07cfad6b37da6e79349c6e3a0316a83b
  depends:
  - libgfortran5 15.1.0 hfa3c126_1
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 133973
  timestamp: 1756239628906
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-15.1.0-hfdf1602_1.conda
  sha256: 981e3fac416e80b007a2798d6c1d4357ebebeb72a039aca1fb3a7effe9dcae86
  md5: c98207b6e2b1a309abab696d229f163e
  depends:
  - libgfortran5 15.1.0 hb74de2c_1
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 134383
  timestamp: 1756239485494
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_5.conda
  sha256: 9d06adc6d8e8187ddc1cad87525c690bc8202d8cb06c13b76ab2fc80a35ed565
  md5: fbd4008644add05032b6764807ee2cba
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 1564589
  timestamp: 1757042559498
- conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran5-15.1.0-hfa3c126_1.conda
  sha256: c4bb79d9e9be3e3a335282b50d18a7965e2a972b95508ea47e4086f1fd699342
  md5: 696e408f36a5a25afdb23e862053ca82
  depends:
  - llvm-openmp >=8.0.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 1225193
  timestamp: 1756238834726
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-15.1.0-hb74de2c_1.conda
  sha256: 1f8f5b2fdd0d2559d0f3bade8da8f57e9ee9b54685bd6081c6d6d9a2b0239b41
  md5: 4281bd1c654cb4f5cab6392b3330451f
  depends:
  - llvm-openmp >=8.0.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 759679
  timestamp: 1756238772083
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_5.conda
  sha256: 125051d51a8c04694d0830f6343af78b556dd88cc249dfec5a97703ebfb1832d
  md5: dcd5ff1940cd38f6df777cac86819d60
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 447215
  timestamp: 1757042483384
- conda: https://conda.anaconda.org/conda-forge/win-64/libhwloc-2.12.1-default_h88281d1_1000.conda
  sha256: 2fb437b82912c74b4869b66c601d52c77bb3ee8cb4812eab346d379f1c823225
  md5: e6298294e7612eccf57376a0683ddc80
  depends:
  - libwinpthread >=12.0.0.r4.gg4f2fc60ca
  - libxml2 >=2.13.8,<2.14.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 2412139
  timestamp: 1752762145331
- conda: https://conda.anaconda.org/conda-forge/win-64/libiconv-1.18-hc1393d2_2.conda
  sha256: 0dcdb1a5f01863ac4e8ba006a8b0dc1a02d2221ec3319b5915a1863254d7efa7
  md5: 64571d1dd6cdcfa25d0664a5950fdaa2
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: LGPL-2.1-only
  purls: []
  size: 696926
  timestamp: 1754909290005
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-35_h7ac8fdf_openblas.conda
  build_number: 35
  sha256: 3967e62d4d1d5c1492f861864afca95aaa8cac14e696ce7b9be7d0b6a50507e8
  md5: 88fa5489509c1da59ab2ee6b234511a5
  depends:
  - libblas 3.9.0 35_h59b9bed_openblas
  constrains:
  - blas 2.135   openblas
  - libcblas   3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 16920
  timestamp: 1757002835750
- conda: https://conda.anaconda.org/conda-forge/osx-64/liblapack-3.9.0-35_h236ab99_openblas.conda
  build_number: 35
  sha256: 16f4aaee86912defb0f695b5e6fca201cb5fe7e33c7d02bcb6a7d30ef2b28a39
  md5: 4ca37f2f14381acc4e5a0741b92aa14b
  depends:
  - libblas 3.9.0 35_h7f60823_openblas
  constrains:
  - blas 2.135   openblas
  - libcblas   3.9.0   35*_openblas
  - liblapacke 3.9.0   35*_openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17155
  timestamp: 1757003625275
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-35_hc9a63f6_openblas.conda
  build_number: 35
  sha256: 84f1c11187b564a9fdf464dad46d436ade966262e3000f7c5037b56b244f6fb8
  md5: 437d6c679b3d959d87b3b735fcc0b4ee
  depends:
  - libblas 3.9.0 35_h10e41b3_openblas
  constrains:
  - liblapacke 3.9.0   35*_openblas
  - libcblas   3.9.0   35*_openblas
  - blas 2.135   openblas
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17166
  timestamp: 1757003647724
- conda: https://conda.anaconda.org/conda-forge/win-64/liblapack-3.9.0-35_hf9ab0e9_mkl.conda
  build_number: 35
  sha256: 56e0992fb58eed8f0d5fa165b8621fa150b84aa9af1467ea0a7a9bb7e2fced4f
  md5: 0c6ed9d722cecda18f50f17fb3c30002
  depends:
  - libblas 3.9.0 35_h5709861_mkl
  constrains:
  - blas 2.135   mkl
  - libcblas   3.9.0   35*_mkl
  - liblapacke 3.9.0   35*_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 78485
  timestamp: 1757003541803
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/osx-64/liblzma-5.8.1-hd471939_2.conda
  sha256: 7e22fd1bdb8bf4c2be93de2d4e718db5c548aa082af47a7430eb23192de6bb36
  md5: 8468beea04b9065b9807fc8b9cdc5894
  depends:
  - __osx >=10.13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 104826
  timestamp: 1749230155443
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
  sha256: 0cb92a9e026e7bd4842f410a5c5c665c89b2eb97794ffddba519a626b8ce7285
  md5: d6df911d4564d77c4374b02552cb17d1
  depends:
  - __osx >=11.0
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 92286
  timestamp: 1749230283517
- conda: https://conda.anaconda.org/conda-forge/win-64/liblzma-5.8.1-h2466b09_2.conda
  sha256: 55764956eb9179b98de7cc0e55696f2eff8f7b83fc3ebff5e696ca358bca28cc
  md5: c15148b2e18da456f5108ccb5e411446
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 104935
  timestamp: 1749230611612
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
  sha256: 927fe72b054277cde6cb82597d0fcf6baf127dcbce2e0a9d8925a68f1265eef5
  md5: d864d34357c3b65a4b731f78c0801dc4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  license_family: GPL
  purls: []
  size: 33731
  timestamp: 1750274110928
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_2.conda
  sha256: 1b51d1f96e751dc945cc06f79caa91833b0c3326efe24e9b506bd64ef49fc9b0
  md5: dfc5aae7b043d9f56ba99514d5e60625
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 5938936
  timestamp: 1755474342204
- conda: https://conda.anaconda.org/conda-forge/osx-64/libopenblas-0.3.30-openmp_h83c2472_2.conda
  sha256: 341dd45c2e88261f1f9ff76c3410355b4b0e894abe6ac89f7cbf64a3d10f0f01
  md5: 89edf77977f520c4245567460d065821
  depends:
  - __osx >=10.13
  - libgfortran
  - libgfortran5 >=14.3.0
  - llvm-openmp >=19.1.7
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6262457
  timestamp: 1755473612572
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_h60d53f8_2.conda
  sha256: 7b8551a4d21cf0b19f9a162f1f283a201b17f1bd5a6579abbd0d004788c511fa
  md5: d004259fd8d3d2798b16299d6ad6c9e9
  depends:
  - __osx >=11.0
  - libgfortran
  - libgfortran5 >=14.3.0
  - llvm-openmp >=19.1.7
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 4284696
  timestamp: 1755471861128
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda
  sha256: 6d9c32fc369af5a84875725f7ddfbfc2ace795c28f246dc70055a79f9b2003da
  md5: 0b367fad34931cb79e0d6b7e5c06bb1c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  purls: []
  size: 932581
  timestamp: 1753948484112
- conda: https://conda.anaconda.org/conda-forge/osx-64/libsqlite-3.50.4-h39a8b3b_0.conda
  sha256: 466366b094c3eb4b1d77320530cbf5400e7a10ab33e4824c200147488eebf7a6
  md5: 156bfb239b6a67ab4a01110e6718cbc4
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  purls: []
  size: 980121
  timestamp: 1753948554003
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.4-h4237e3c_0.conda
  sha256: 802ebe62e6bc59fc26b26276b793e0542cfff2d03c086440aeaf72fb8bbcec44
  md5: 1dcb0468f5146e38fae99aef9656034b
  depends:
  - __osx >=11.0
  - icu >=75.1,<76.0a0
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  purls: []
  size: 902645
  timestamp: 1753948599139
- conda: https://conda.anaconda.org/conda-forge/win-64/libsqlite-3.50.4-hf5d6505_0.conda
  sha256: 5dc4f07b2d6270ac0c874caec53c6984caaaa84bc0d3eb593b0edf3dc8492efa
  md5: ccb20d946040f86f0c05b644d5eadeca
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: blessing
  purls: []
  size: 1288499
  timestamp: 1753948889360
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_5.conda
  sha256: 0f5f61cab229b6043541c13538d75ce11bd96fb2db76f94ecf81997b1fde6408
  md5: 4e02a49aaa9d5190cb630fa43528fbe6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 3896432
  timestamp: 1757042571458
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_5.conda
  sha256: 7b8cabbf0ab4fe3581ca28fe8ca319f964078578a51dd2ca3f703c1d21ba23ff
  md5: 8bba50c7f4679f08c861b597ad2bda6b
  depends:
  - libstdcxx 15.1.0 h8f9b012_5
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29233
  timestamp: 1757042603319
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb03c661_1.conda
  sha256: c180f4124a889ac343fc59d15558e93667d894a966ec6fdb61da1604481be26b
  md5: 0f03292cc56bf91a077a134ea8747118
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  license: MIT
  license_family: MIT
  purls: []
  size: 895108
  timestamp: 1753948278280
- conda: https://conda.anaconda.org/conda-forge/osx-64/libuv-1.51.0-h58003a5_1.conda
  sha256: d90dd0eee6f195a5bd14edab4c5b33be3635b674b0b6c010fb942b956aa2254c
  md5: fbfc6cf607ae1e1e498734e256561dc3
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  purls: []
  size: 422612
  timestamp: 1753948458902
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libuv-1.51.0-h6caf38d_1.conda
  sha256: 042c7488ad97a5629ec0a991a8b2a3345599401ecc75ad6a5af73b60e6db9689
  md5: c0d87c3c8e075daf1daf6c31b53e8083
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  purls: []
  size: 421195
  timestamp: 1753948426421
- conda: https://conda.anaconda.org/conda-forge/win-64/libwinpthread-12.0.0.r4.gg4f2fc60ca-h57928b3_9.conda
  sha256: 373f2973b8a358528b22be5e8d84322c165b4c5577d24d94fd67ad1bb0a0f261
  md5: 08bfa5da6e242025304b206d152479ef
  depends:
  - ucrt
  constrains:
  - pthreads-win32 <0.0a0
  - msys2-conda-epoch <0.0a0
  license: MIT AND BSD-3-Clause-Clear
  purls: []
  size: 35794
  timestamp: 1737099561703
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  purls: []
  size: 100393
  timestamp: 1702724383534
- conda: https://conda.anaconda.org/conda-forge/win-64/libxml2-2.13.8-h741aa76_1.conda
  sha256: 32fa908bb2f2a6636dab0edaac1d4bf5ff62ad404a82d8bb16702bc5b8eb9114
  md5: aeb49dc1f5531de13d2c0d57ffa6d0c8
  depends:
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: MIT
  license_family: MIT
  purls: []
  size: 1519401
  timestamp: 1754315497781
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/osx-64/libzlib-1.3.1-hd23fc13_2.conda
  sha256: 8412f96504fc5993a63edf1e211d042a1fd5b1d51dedec755d2058948fcced09
  md5: 003a54a4e32b02f7355b50a837e699da
  depends:
  - __osx >=10.13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 57133
  timestamp: 1727963183990
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
  sha256: ce34669eadaba351cd54910743e6a2261b67009624dbc7daeeafdef93616711b
  md5: 369964e85dc26bfe78f41399b366c435
  depends:
  - __osx >=11.0
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 46438
  timestamp: 1727963202283
- conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-1.3.1-h2466b09_2.conda
  sha256: ba945c6493449bed0e6e29883c4943817f7c79cbff52b83360f7b341277c6402
  md5: 41fbfac52c601159df6c01f875de31b9
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 55476
  timestamp: 1727963768015
- conda: https://conda.anaconda.org/conda-forge/osx-64/llvm-openmp-21.1.0-hf4e0ed4_0.conda
  sha256: 78336131a08990390003ef05d14ecb49f3a47e4dac60b1bcebeccd87fa402925
  md5: 5acc6c266fd33166fa3b33e48665ae0d
  depends:
  - __osx >=10.13
  constrains:
  - openmp 21.1.0|21.1.0.*
  - intel-openmp <0.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  purls: []
  size: 311174
  timestamp: 1756673275570
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-21.1.0-hbb9b287_0.conda
  sha256: c6750073a128376a14bedacfa90caab4c17025c9687fcf6f96e863b28d543af4
  md5: e57d95fec6eaa747e583323cba6cfe5c
  depends:
  - __osx >=11.0
  constrains:
  - intel-openmp <0.0a0
  - openmp 21.1.0|21.1.0.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  purls: []
  size: 286039
  timestamp: 1756673290280
- conda: https://conda.anaconda.org/conda-forge/win-64/llvm-openmp-20.1.8-hfa2b4ca_2.conda
  sha256: 8970b7f9057a1c2c18bfd743c6f5ce73b86197d7724423de4fa3d03911d5874b
  md5: 2dc2edf349464c8b83a576175fc2ad42
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  constrains:
  - intel-openmp <0.0a0
  - openmp 20.1.8|20.1.8.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  purls: []
  size: 344490
  timestamp: 1756145011384
- conda: https://conda.anaconda.org/conda-forge/win-64/mkl-2024.2.2-h57928b3_16.conda
  sha256: ce841e7c3898764154a9293c0f92283c1eb28cdacf7a164c94b632a6af675d91
  md5: 5cddc979c74b90cf5e5cda4f97d5d8bb
  depends:
  - llvm-openmp >=20.1.8
  - tbb 2021.*
  license: LicenseRef-IntelSimplifiedSoftwareOct2022
  license_family: Proprietary
  purls: []
  size: 103088799
  timestamp: 1753975600547
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/osx-64/ncurses-6.5-h0622a9a_3.conda
  sha256: ea4a5d27ded18443749aefa49dc79f6356da8506d508b5296f60b8d51e0c4bd9
  md5: ced34dd9929f491ca6dab6a2927aff25
  depends:
  - __osx >=10.13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 822259
  timestamp: 1738196181298
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
  sha256: 2827ada40e8d9ca69a153a45f7fd14f32b2ead7045d3bbb5d10964898fe65733
  md5: 068d497125e4bf8a66bf707254fff5ae
  depends:
  - __osx >=11.0
  license: X11 AND BSD-3-Clause
  purls: []
  size: 797030
  timestamp: 1738196177597
- conda: https://conda.anaconda.org/conda-forge/linux-64/nodejs-24.4.1-heeeca48_0.conda
  sha256: 1239ba36ea69eefcc55f107fe186810b59488923544667175f6976fa4903c8c9
  md5: d629b201c3fbc0c203ca0ad7b03f22ce
  depends:
  - libgcc >=14
  - __glibc >=2.28,<3.0.a0
  - libstdcxx >=14
  - libgcc >=14
  - libuv >=1.51.0,<2.0a0
  - icu >=75.1,<76.0a0
  - openssl >=3.5.1,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 25669735
  timestamp: 1752839464718
- conda: https://conda.anaconda.org/conda-forge/osx-64/nodejs-24.4.1-h2e7699b_0.conda
  sha256: 1c9571726b5b5e85acfba50dda7ae9b22d2b29e590159a581bafde5bf2e04621
  md5: 9993063cfe84cf1fa928c7d021bd01a0
  depends:
  - __osx >=10.15
  - libcxx >=19
  - openssl >=3.5.1,<4.0a0
  - libuv >=1.51.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - icu >=75.1,<76.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 18918546
  timestamp: 1752839437994
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/nodejs-24.4.1-hab9d20b_0.conda
  sha256: c79d2c81f80a9adedc77362f2e8b10879ed0f9806deb6ba2464c1287a05f0b9b
  md5: 463a537de602f8558604f27395b323d0
  depends:
  - libcxx >=19
  - __osx >=11.0
  - openssl >=3.5.1,<4.0a0
  - libuv >=1.51.0,<2.0a0
  - icu >=75.1,<76.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 17949155
  timestamp: 1752839389217
- conda: https://conda.anaconda.org/conda-forge/win-64/nodejs-24.4.1-he453025_0.conda
  sha256: 1bb0d9e370bb0ffa2071ccfdd0ef3cb90bd183b07c67b646d1aa5c743004d233
  md5: cde0d5793a73ab343b5764fa6c002771
  license: MIT
  license_family: MIT
  purls: []
  size: 29967122
  timestamp: 1752839409586
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py311h2e04523_2.conda
  sha256: a8be67f8be3354e33da3adacb021f7f93971ddc466d79eab412151bf5dc58cf4
  md5: 7fb6248106c55e3ccd29ac675be01fac
  depends:
  - python
  - libgcc >=14
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libcblas >=3.9.0,<4.0a0
  - libblas >=3.9.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=hash-mapping
  size: 9415269
  timestamp: 1756343065236
- conda: https://conda.anaconda.org/conda-forge/osx-64/numpy-2.3.2-py311h09fcace_2.conda
  sha256: 7db239350fcce5f16bcb2891578be74a17cea9aa61506b427d3d0fc528b0a18f
  md5: 3fd67cc81b4297d403e3bfde14ab409e
  depends:
  - python
  - __osx >=10.13
  - libcxx >=19
  - liblapack >=3.9.0,<4.0a0
  - libblas >=3.9.0,<4.0a0
  - python_abi 3.11.* *_cp311
  - libcblas >=3.9.0,<4.0a0
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=hash-mapping
  size: 8551576
  timestamp: 1756343060935
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.2-py311h0856f98_2.conda
  sha256: b2cb7fc23d462840fec513fbb5ca4467a2d8b3e9ab2588096443b80d9f286058
  md5: c55c144bf8ee31580fe4fecaab9043a5
  depends:
  - python
  - python 3.11.* *_cpython
  - __osx >=11.0
  - libcxx >=19
  - liblapack >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libblas >=3.9.0,<4.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=compressed-mapping
  size: 7274422
  timestamp: 1756343068339
- conda: https://conda.anaconda.org/conda-forge/win-64/numpy-2.3.2-py311h80b3fa1_2.conda
  sha256: cfce275b0959d2de5abeaa5892ec3192a3aa38600c8e12d962c9c4a4ac838106
  md5: 7ad07d0ab1888f4e4dc95b330a51bffd
  depends:
  - python
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  - ucrt >=10.0.20348.0
  - libblas >=3.9.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - python_abi 3.11.* *_cp311
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=hash-mapping
  size: 8019925
  timestamp: 1756343088463
- pypi: https://files.pythonhosted.org/packages/00/e1/47887212baa7bc0532880d33d5eafbdb46fcc4b53789b903282a74a85b5b/openai-1.106.1-py3-none-any.whl
  name: openai
  version: 1.106.1
  sha256: bfdef37c949f80396c59f2c17e0eda35414979bc07ef3379596a93c9ed044f3a
  requires_dist:
  - anyio>=3.5.0,<5
  - distro>=1.7.0,<2
  - httpx>=0.23.0,<1
  - jiter>=0.4.0,<1
  - pydantic>=1.9.0,<3
  - sniffio
  - tqdm>4
  - typing-extensions>=4.11,<5
  - aiohttp ; extra == 'aiohttp'
  - httpx-aiohttp>=0.1.8 ; extra == 'aiohttp'
  - numpy>=1 ; extra == 'datalib'
  - pandas-stubs>=******** ; extra == 'datalib'
  - pandas>=1.2.3 ; extra == 'datalib'
  - websockets>=13,<16 ; extra == 'realtime'
  - numpy>=2.0.2 ; extra == 'voice-helpers'
  - sounddevice>=0.5.1 ; extra == 'voice-helpers'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.2-h26f9b46_0.conda
  sha256: c9f54d4e8212f313be7b02eb962d0cb13a8dae015683a403d3accd4add3e520e
  md5: ffffb341206dd0dab0c36053c048d621
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=14
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3128847
  timestamp: 1754465526100
- conda: https://conda.anaconda.org/conda-forge/osx-64/openssl-3.5.2-h6e31bce_0.conda
  sha256: 8be57a11019666aa481122c54e29afd604405b481330f37f918e9fbcd145ef89
  md5: 22f5d63e672b7ba467969e9f8b740ecd
  depends:
  - __osx >=10.13
  - ca-certificates
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 2743708
  timestamp: 1754466962243
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.2-he92f556_0.conda
  sha256: f6d1c87dbcf7b39fad24347570166dade1c533ae2d53c60a70fa4dc874ef0056
  md5: bcb0d87dfbc199d0a461d2c7ca30b3d8
  depends:
  - __osx >=11.0
  - ca-certificates
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3074848
  timestamp: 1754465710470
- conda: https://conda.anaconda.org/conda-forge/win-64/openssl-3.5.2-h725018a_0.conda
  sha256: 2413f3b4606018aea23acfa2af3c4c46af786739ab4020422e9f0c2aec75321b
  md5: 150d3920b420a27c0848acca158f94dc
  depends:
  - ca-certificates
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 9275175
  timestamp: 1754467904482
- conda: https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda
  sha256: ec9ed3cef137679f3e3a68e286c6efd52144684e1be0b05004d9699882dadcdd
  md5: dfce4b2af4bfe90cdcaf56ca0b28ddf5
  depends:
  - python >=3.9,<3.13.0a0
  - setuptools
  - wheel
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pip?source=hash-mapping
  size: 1177168
  timestamp: 1753924973872
- pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
  name: pydantic
  version: 2.11.7
  sha256: dde5df002701f6de26248661f6835bbe296a47bf73990135c7d07ce741b9623b
  requires_dist:
  - annotated-types>=0.6.0
  - pydantic-core==2.33.2
  - typing-extensions>=4.12.2
  - typing-inspection>=0.4.0
  - email-validator>=2.0.0 ; extra == 'email'
  - tzdata ; python_full_version >= '3.9' and sys_platform == 'win32' and extra == 'timezone'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/24/2f/3cfa7244ae292dd850989f328722d2aef313f74ffc471184dc509e1e4e5a/pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: e799c050df38a639db758c617ec771fd8fb7a5f8eaaa4b27b101f266b216a246
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/3f/8d/71db63483d518cbbf290261a1fc2839d17ff89fce7089e08cad07ccfce67/pydantic_core-2.33.2-cp311-cp311-macosx_10_12_x86_64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: 4c5b0a576fb381edd6d27f0a85915c6daf2f8138dc5c267a57c08a62900758c7
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/47/bc/cd720e078576bdb8255d5032c5d63ee5c0bf4b7173dd955185a1d658c456/pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: 881b21b5549499972441da4758d662aeea93f1923f953e9cbaff14b8b9565aef
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/fe/1b/25b7cccd4519c0b23c2dd636ad39d381abf113085ce4f7bec2b0dc755eb1/pydantic_core-2.33.2-cp311-cp311-win_amd64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: 1e063337ef9e9820c77acc768546325ebe04ee38b08703244c1309cccc4f1bab
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.11.13-h9e4cc4f_0_cpython.conda
  sha256: 9979a7d4621049388892489267139f1aa629b10c26601ba5dce96afc2b1551d4
  md5: 8c399445b6dc73eab839659e6c7b5ad1
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  purls: []
  size: 30629559
  timestamp: 1749050021812
- conda: https://conda.anaconda.org/conda-forge/osx-64/python-3.11.13-h9ccd52b_0_cpython.conda
  sha256: d8e15db837c10242658979bc475298059bd6615524f2f71365ab8e54fbfea43c
  md5: 6e28c31688c6f1fdea3dc3d48d33e1c0
  depends:
  - __osx >=10.13
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  purls: []
  size: 15423460
  timestamp: 1749049420299
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.11.13-hc22306f_0_cpython.conda
  sha256: 2c966293ef9e97e66b55747c7a97bc95ba0311ac1cf0d04be4a51aafac60dcb1
  md5: 95facc4683b7b3b9cf8ae0ed10f30dce
  depends:
  - __osx >=11.0
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  purls: []
  size: 14573820
  timestamp: 1749048947732
- conda: https://conda.anaconda.org/conda-forge/win-64/python-3.11.13-h3f84c4b_0_cpython.conda
  sha256: 723dbca1384f30bd2070f77dd83eefd0e8d7e4dda96ac3332fbf8fe5573a8abb
  md5: bedbb6f7bb654839719cd528f9b298ad
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - python_abi 3.11.* *_cp311
  license: Python-2.0
  purls: []
  size: 18242669
  timestamp: 1749048351218
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.11-8_cp311.conda
  build_number: 8
  sha256: fddf123692aa4b1fc48f0471e346400d9852d96eeed77dbfdd746fa50a8ff894
  md5: 8fcb6b0e2161850556231336dae58358
  constrains:
  - python 3.11.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 7003
  timestamp: 1752805919375
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/osx-64/readline-8.2-h7cca4af_2.conda
  sha256: 53017e80453c4c1d97aaf78369040418dea14cf8f46a2fa999f31bd70b36c877
  md5: 342570f8e02f2f022147a7f841475784
  depends:
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 256712
  timestamp: 1740379577668
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
  sha256: 7db04684d3904f6151eff8673270922d31da1eea7fa73254d01c437f49702e34
  md5: 63ef3f6e6d6d5c589e64f11263dc5676
  depends:
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 252359
  timestamp: 1740379663071
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
  sha256: 972560fcf9657058e3e1f97186cc94389144b46dbdf58c807ce62e83f977e863
  md5: 4de79c071274a53dcaf2a8c749d1499e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/setuptools?source=hash-mapping
  size: 748788
  timestamp: 1748804951958
- pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
  name: sniffio
  version: 1.3.1
  sha256: 2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/win-64/tbb-2021.13.0-h18a62a1_3.conda
  sha256: 30e82640a1ad9d9b5bee006da7e847566086f8fdb63d15b918794a7ef2df862c
  md5: 72226638648e494aaafde8155d50dab2
  depends:
  - libhwloc >=2.12.1,<2.12.2.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.3,<15
  - vc14_runtime >=14.44.35208
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 150266
  timestamp: 1755776172092
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/osx-64/tk-8.6.13-hf689a15_2.conda
  sha256: b24468006a96b71a5f4372205ea7ec4b399b0f2a543541e86f883de54cd623fc
  md5: 9864891a6946c2fe037c02fca7392ab4
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3259809
  timestamp: 1748387843735
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
  sha256: cb86c522576fa95c6db4c878849af0bccfd3264daf0cc40dd18e7f4a7bfced0e
  md5: 7362396c170252e7b7b0c8fb37fe9c78
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3125538
  timestamp: 1748388189063
- conda: https://conda.anaconda.org/conda-forge/win-64/tk-8.6.13-h2c6b04d_2.conda
  sha256: e3614b0eb4abcc70d98eae159db59d9b4059ed743ef402081151a948dce95896
  md5: ebd0e761de9aa879a51d22cc721bd095
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: TCL
  license_family: BSD
  purls: []
  size: 3466348
  timestamp: 1748388121356
- pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
  name: tqdm
  version: 4.67.1
  sha256: 26445eca388f82e72884e0d580d5464cd801a3ea01e63e5601bdff9ba6a48de2
  requires_dist:
  - colorama ; sys_platform == 'win32'
  - pytest>=6 ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - pytest-timeout ; extra == 'dev'
  - pytest-asyncio>=0.24 ; extra == 'dev'
  - nbval ; extra == 'dev'
  - requests ; extra == 'discord'
  - slack-sdk ; extra == 'slack'
  - requests ; extra == 'telegram'
  - ipywidgets>=6 ; extra == 'notebook'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/18/67/36e9267722cc04a6b9f15c7f3441c2363321a3ea07da7ae0c0707beb2a9c/typing_extensions-4.15.0-py3-none-any.whl
  name: typing-extensions
  version: 4.15.0
  sha256: f0fa19c6845758ab08074a0cfa8b7aecb71c999ca73d62883bc25cc018c4e548
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
  name: typing-inspection
  version: 0.4.1
  sha256: 389055682238f53b04f7badcb49b989835495a96700ced5dab2d8feae4b26f51
  requires_dist:
  - typing-extensions>=4.12.0
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  purls: []
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.26100.0-h57928b3_0.conda
  sha256: 3005729dce6f3d3f5ec91dfc49fc75a0095f9cd23bab49efb899657297ac91a5
  md5: 71b24316859acd00bdb8b38f5e2ce328
  constrains:
  - vc14_runtime >=14.29.30037
  - vs2015_runtime >=14.29.30037
  license: LicenseRef-MicrosoftWindowsSDK10
  purls: []
  size: 694692
  timestamp: 1756385147981
- conda: https://conda.anaconda.org/conda-forge/win-64/vc-14.3-h41ae7f8_31.conda
  sha256: cb357591d069a1e6cb74199a8a43a7e3611f72a6caed9faa49dbb3d7a0a98e0b
  md5: 28f4ca1e0337d0f27afb8602663c5723
  depends:
  - vc14_runtime >=14.44.35208
  track_features:
  - vc14
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 18249
  timestamp: 1753739241465
- conda: https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.44.35208-h818238b_31.conda
  sha256: af4b4b354b87a9a8d05b8064ff1ea0b47083274f7c30b4eb96bc2312c9b5f08f
  md5: 603e41da40a765fd47995faa021da946
  depends:
  - ucrt >=10.0.20348.0
  - vcomp14 14.44.35208 h818238b_31
  constrains:
  - vs2015_runtime 14.44.35208.* *_31
  license: LicenseRef-MicrosoftVisualCpp2015-2022Runtime
  license_family: Proprietary
  purls: []
  size: 682424
  timestamp: 1753739239305
- conda: https://conda.anaconda.org/conda-forge/win-64/vcomp14-14.44.35208-h818238b_31.conda
  sha256: 67b317b64f47635415776718d25170a9a6f9a1218c0f5a6202bfd687e07b6ea4
  md5: a6b1d5c1fc3cb89f88f7179ee6a9afe3
  depends:
  - ucrt >=10.0.20348.0
  constrains:
  - vs2015_runtime 14.44.35208.* *_31
  license: LicenseRef-MicrosoftVisualCpp2015-2022Runtime
  license_family: Proprietary
  purls: []
  size: 113963
  timestamp: 1753739198723
- conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda
  sha256: 1b34021e815ff89a4d902d879c3bd2040bc1bd6169b32e9427497fa05c55f1ce
  md5: 75cb7132eb58d97896e173ef12ac9986
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wheel?source=hash-mapping
  size: 62931
  timestamp: 1733130309598
