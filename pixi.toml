[project]
name = "codex-env"
version = "0.1.0"
description = "Global environment for OpenAI Codex with Node.js and numpy"
channels = ["conda-forge", "pytorch"]
platforms = ["linux-64", "osx-64", "osx-arm64", "win-64"]

[dependencies]
python = ">=3.8,<3.12"
nodejs = ">=18"
numpy = "*"
pip = "*"
pandas = "*"
matplotlib = "*"
seaborn = "*"
scipy = "*"
jupyter = "*"

[pypi-dependencies]
openai = "*"

[tasks]
codex = "python -c 'import openai; print(\"OpenAI Codex environment ready!\")'"
setup = "pip install openai"
test-env = "python -c 'import numpy; import openai; print(f\"Environment ready! NumPy: {numpy.__version__}, OpenAI: {openai.__version__}\")'"
# Viral analysis tasks
analyze = { cmd = "python viral_copy_analysis.py" }
notebook = { cmd = "jupyter notebook --ip=0.0.0.0 --no-browser" }

[environments]
default = { solve-group = "default" }

[activation]
scripts = ["scripts/activate.sh"]
