#!/usr/bin/env bash
set -euo pipefail

# Generate coverage TSV and a simple PNG plot using gnuplot from a BAM

THREADS=${THREADS:-8}
IN_BAM=${IN_BAM:-FSRM_only/combined/FSRM_all.single_run.sorted.bam}
OUT_PREFIX=${OUT_PREFIX:-${IN_BAM%.bam}}

if [[ ! -s "$IN_BAM" ]]; then
  echo "[ERROR] BAM not found: $IN_BAM" >&2
  exit 1
fi

TSV="${OUT_PREFIX}.coverage.tsv"
PNG="${OUT_PREFIX}.coverage.png"

echo "[INFO] Computing per-base coverage -> $TSV"
samtools depth -a -@ "$THREADS" "$IN_BAM" > "$TSV"

# If multiple contigs exist, gnuplot will plot them sequentially along x via an index
GNUPLOT_SCRIPT="${OUT_PREFIX}.gnuplot"
cat > "$GNUPLOT_SCRIPT" <<'GP'
set terminal pngcairo size 1600,500
set output OUTPNG
set datafile separator '\t'
set title TITLE
set xlabel 'Position (concatenated across contigs)'
set ylabel 'Depth'
set grid
plot INTSV using 0:3 with lines lc rgb '#1f78b4' title 'Coverage'
GP

echo "[INFO] Plotting coverage -> $PNG"
gnuplot -e "OUTPNG='${PNG}'; INTSV='${TSV}'; TITLE='Coverage: ${IN_BAM}'" "$GNUPLOT_SCRIPT"

echo "[INFO] Coverage done: TSV=$TSV PNG=$PNG"

