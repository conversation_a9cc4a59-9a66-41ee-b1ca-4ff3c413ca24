#!/bin/bash
# Activation script for OpenAI Codex environment

echo "Activating OpenAI Codex environment..."
echo "Node.js version: $(node --version)"
echo "Python version: $(python --version)"
echo "NumPy available: $(python -c 'import numpy; print(numpy.__version__)' 2>/dev/null || echo 'Not installed')"

# Set environment variables for OpenAI
export OPENAI_API_BASE="https://api.openai.com/v1"

# Add any additional setup here
echo "Environment ready for OpenAI Codex development!"
