#!/usr/bin/env bash
# Guide RNA design pipeline wrapper for ppdesign tools
# Usage:
#   bash scripts/run_ppdesign_pipeline.sh [INPUT_FASTA] [OUTPUT_DIR]
# Example:
#   bash scripts/run_ppdesign_pipeline.sh \
#     /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/pathdb.fna \
#     /clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1

set -euo pipefail

INPUT_FASTA=${1:-/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/pathdb.fna}
OUTPUT_DIR=${2:-/clusterfs/jgi/scratch/science/mgs/nelli/roniya/ww_viral_abundance_analysis/ppdesign/test1}

# Tunable parameters (override via env vars)
MIN_GUIDES=${MIN_GUIDES:-5}
MAX_GUIDES=${MAX_GUIDES:-20}
CONSERVATION=${CONSERVATION:-0.70}
PAM=${PAM:-spCas9}
MIN_GC=${MIN_GC:-40}
MAX_GC=${MAX_GC:-60}
CLUSTER_THRESHOLD=${CLUSTER_THRESHOLD:-0.85}
MIN_ADDITIONAL_COVERAGE=${MIN_ADDITIONAL_COVERAGE:-0.01}
REQUIRED_TARGETS=${REQUIRED_TARGETS:-NC_003630_1|Pepper_mild_mottle_virus}

echo "[ppdesign] Input FASTA      : ${INPUT_FASTA}"
echo "[ppdesign] Output directory : ${OUTPUT_DIR}"

if [[ ! -f "${INPUT_FASTA}" ]]; then
  echo "ERROR: Input FASTA not found: ${INPUT_FASTA}" >&2
  exit 1
fi

mkdir -p "${OUTPUT_DIR}"

# Choose runner: prefer pixi tasks if available, else bare CLIs
run_cmd() {
  if command -v pixi >/dev/null 2>&1; then
    echo "+ pixi run $*"
    pixi run "$@"
  else
    echo "+ $*"
    "$@"
  fi
}

echo "[ppdesign] Step 1: Design guide RNAs"
run_cmd ppdesign-grna \
  --fasta-input "${INPUT_FASTA}" \
  --output-dir "${OUTPUT_DIR}" \
  --conservation "${CONSERVATION}" \
  --no-degenerate \
  --min-guides "${MIN_GUIDES}" \
  --max-guides "${MAX_GUIDES}" \
  --pam-type "${PAM}" \
  --min-gc "${MIN_GC}" --max-gc "${MAX_GC}" \
  --cluster-threshold "${CLUSTER_THRESHOLD}" \
  --min-additional-coverage "${MIN_ADDITIONAL_COVERAGE}" \
  --required-targets "${REQUIRED_TARGETS}"

GUIDE_CSV="${OUTPUT_DIR}/guide_rnas.csv"
GUIDE_FASTA="${OUTPUT_DIR}/guide_rnas.fasta"

if [[ ! -s "${GUIDE_CSV}" || ! -s "${GUIDE_FASTA}" ]]; then
  echo "ERROR: Expected outputs not found after design: ${GUIDE_CSV} and/or ${GUIDE_FASTA}" >&2
  exit 2
fi

echo "[ppdesign] Step 2: Validate guide RNAs (alignment stats)"
run_cmd validate-grna \
  --guide-csv "${GUIDE_CSV}" \
  --guide-fasta "${GUIDE_FASTA}" \
  --target-fasta "${INPUT_FASTA}"

echo "[ppdesign] Step 3: Thermodynamic analysis"
run_cmd validate-grna-thermo \
  --guide-csv "${GUIDE_CSV}" \
  --guide-fasta "${GUIDE_FASTA}" \
  --target-fasta "${INPUT_FASTA}"

echo "[ppdesign] Pipeline complete. Outputs in: ${OUTPUT_DIR}"

