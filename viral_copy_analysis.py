#!/usr/bin/env python3
"""
Viral Copy Number Analysis Script
Analyzes viral copy numbers across different treatments and creates professional visualizations
Author: Senior Bioinformatician
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set style for professional plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

def load_and_clean_data(file_path):
    """Load and clean the viral copy data"""
    # Read the data
    df = pd.read_csv(file_path, sep='\t')
    
    # Remove duplicate headers if present
    df = df[df['Sample'] != 'Sample']
    
    # Convert Copies to numeric
    df['Copies'] = pd.to_numeric(df['Copies'])
    
    # Extract virus type and sample info
    df['Virus_Type'] = df['Sample'].str.extract(r'^([^-]+)')
    df['Sample_ID'] = df['Sample'].str.extract(r'([^:]+):(\d+)')[0]
    df['Replicate'] = df['Sample'].str.extract(r':(\d+)$')[0]
    
    return df

def create_boxplot(df, output_path='viral_copies_boxplot.png'):
    """Create professional boxplot for viral copy analysis"""
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    
    # Boxplot by treatment
    sns.boxplot(data=df, x='Treatment', y='Copies', ax=axes[0])
    axes[0].set_title('Viral Copy Numbers by Treatment', fontsize=14, fontweight='bold')
    axes[0].set_xlabel('Treatment', fontsize=12)
    axes[0].set_ylabel('Copy Number', fontsize=12)
    axes[0].tick_params(axis='x', rotation=45)
    axes[0].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e6:.1f}M'))
    
    # Boxplot by virus type and treatment
    sns.boxplot(data=df, x='Treatment', y='Copies', hue='Virus_Type', ax=axes[1])
    axes[1].set_title('Viral Copy Numbers by Treatment and Virus Type', fontsize=14, fontweight='bold')
    axes[1].set_xlabel('Treatment', fontsize=12)
    axes[1].set_ylabel('Copy Number', fontsize=12)
    axes[1].tick_params(axis='x', rotation=45)
    axes[1].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e6:.1f}M'))
    axes[1].legend(title='Virus Type', bbox_to_anchor=(1.05, 1), loc='upper left')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def create_barplot(df, output_path='viral_copies_barplot.png'):
    """Create professional bar chart showing mean values with error bars"""
    # Calculate summary statistics
    summary = df.groupby(['Treatment', 'Virus_Type'])['Copies'].agg(['mean', 'std', 'count']).reset_index()
    summary['sem'] = summary['std'] / np.sqrt(summary['count'])
    
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    
    # Overall treatment comparison
    overall_summary = df.groupby('Treatment')['Copies'].agg(['mean', 'std', 'count']).reset_index()
    overall_summary['sem'] = overall_summary['std'] / np.sqrt(overall_summary['count'])
    
    bars1 = axes[0].bar(overall_summary['Treatment'], overall_summary['mean'], 
                       yerr=overall_summary['sem'], capsize=5, alpha=0.8)
    axes[0].set_title('Mean Viral Copy Numbers by Treatment', fontsize=14, fontweight='bold')
    axes[0].set_xlabel('Treatment', fontsize=12)
    axes[0].set_ylabel('Mean Copy Number', fontsize=12)
    axes[0].tick_params(axis='x', rotation=45)
    axes[0].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e6:.1f}M'))
    
    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        axes[0].text(bar.get_x() + bar.get_width()/2., height,
                    f'{height/1e6:.1f}M', ha='center', va='bottom')
    
    # Grouped bar chart by virus type
    virus_types = summary['Virus_Type'].unique()
    treatments = summary['Treatment'].unique()
    x = np.arange(len(treatments))
    width = 0.35
    
    for i, virus in enumerate(virus_types):
        virus_data = summary[summary['Virus_Type'] == virus]
        bars2 = axes[1].bar(x + i*width, virus_data['mean'], width, 
                           yerr=virus_data['sem'], capsize=5, 
                           label=virus, alpha=0.8)
        
        # Add value labels
        for bar in bars2:
            height = bar.get_height()
            axes[1].text(bar.get_x() + bar.get_width()/2., height,
                        f'{height/1e6:.1f}M', ha='center', va='bottom', fontsize=9)
    
    axes[1].set_title('Mean Viral Copy Numbers by Treatment and Virus Type', fontsize=14, fontweight='bold')
    axes[1].set_xlabel('Treatment', fontsize=12)
    axes[1].set_ylabel('Mean Copy Number', fontsize=12)
    axes[1].set_xticks(x + width/2)
    axes[1].set_xticklabels(treatments, rotation=45)
    axes[1].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e6:.1f}M'))
    axes[1].legend(title='Virus Type')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig, summary

def perform_statistical_analysis(df):
    """Perform statistical analysis and return results"""
    results = {}
    
    # ANOVA for treatment effect
    treatments = df['Treatment'].unique()
    treatment_groups = [df[df['Treatment'] == t]['Copies'] for t in treatments]
    f_stat, p_value = stats.f_oneway(*treatment_groups)
    
    results['anova'] = {
        'f_statistic': f_stat,
        'p_value': p_value,
        'significant': p_value < 0.05
    }
    
    # Pairwise t-tests
    results['pairwise'] = {}
    for i, t1 in enumerate(treatments):
        for t2 in treatments[i+1:]:
            group1 = df[df['Treatment'] == t1]['Copies']
            group2 = df[df['Treatment'] == t2]['Copies']
            t_stat, p_val = stats.ttest_ind(group1, group2)
            results['pairwise'][f'{t1}_vs_{t2}'] = {
                't_statistic': t_stat,
                'p_value': p_val,
                'significant': p_val < 0.05
            }
    
    return results

def generate_summary_report(df, stats_results, summary_stats):
    """Generate a comprehensive summary report"""
    report = []
    report.append("=" * 80)
    report.append("VIRAL COPY NUMBER ANALYSIS REPORT")
    report.append("=" * 80)
    report.append("")

    # Dataset overview
    report.append("DATASET OVERVIEW:")
    report.append(f"Total samples: {len(df)}")
    report.append(f"Virus types: {', '.join(df['Virus_Type'].unique())}")
    report.append(f"Treatments: {', '.join(df['Treatment'].unique())}")
    report.append("")

    # Summary statistics
    report.append("SUMMARY STATISTICS:")
    for _, row in summary_stats.iterrows():
        report.append(f"{row['Virus_Type']} - {row['Treatment']}:")
        report.append(f"  Mean: {row['mean']:,.0f} copies")
        report.append(f"  Std Dev: {row['std']:,.0f} copies")
        report.append(f"  N: {row['count']}")
        report.append("")

    # Statistical analysis
    report.append("STATISTICAL ANALYSIS:")
    report.append(f"ANOVA F-statistic: {stats_results['anova']['f_statistic']:.3f}")
    report.append(f"ANOVA p-value: {stats_results['anova']['p_value']:.3e}")
    report.append(f"Significant treatment effect: {stats_results['anova']['significant']}")
    report.append("")

    report.append("PAIRWISE COMPARISONS:")
    for comparison, result in stats_results['pairwise'].items():
        report.append(f"{comparison.replace('_', ' ')}: p = {result['p_value']:.3e} {'*' if result['significant'] else 'ns'}")

    report.append("")
    report.append("KEY FINDINGS:")

    # Calculate fold changes for each virus type
    ms2_no_treat = summary_stats[(summary_stats['Virus_Type'] == 'MS2') & (summary_stats['Treatment'] == 'No_treatment')]['mean'].iloc[0]
    ms2_guide_rna = summary_stats[(summary_stats['Virus_Type'] == 'MS2') & (summary_stats['Treatment'] == 'guideRNA+Cas9')]['mean'].iloc[0]
    ms2_fold_reduction = ms2_no_treat / ms2_guide_rna

    fivem_no_treat = summary_stats[(summary_stats['Virus_Type'] == '5M') & (summary_stats['Treatment'] == 'No_treatment')]['mean'].iloc[0]
    fivem_guide_rna = summary_stats[(summary_stats['Virus_Type'] == '5M') & (summary_stats['Treatment'] == 'guideRNA+Cas9')]['mean'].iloc[0]
    fivem_fold_reduction = fivem_no_treat / fivem_guide_rna

    report.append(f"- MS2 virus: guideRNA+Cas9 shows {ms2_fold_reduction:.1f}-fold reduction in viral copies")
    report.append(f"- 5M virus: guideRNA+Cas9 shows {fivem_fold_reduction:.1f}-fold reduction in viral copies")
    report.append(f"- MS2 virus appears more susceptible to CRISPR-Cas9 targeting than 5M virus")

    # Correct statistical interpretation
    if stats_results['anova']['significant']:
        report.append(f"- ANOVA indicates significant treatment effects (p < 0.05)")
    else:
        report.append(f"- ANOVA indicates no significant treatment effects overall (p > 0.05)")
        report.append(f"- However, individual virus types may show different responses")

    return "\n".join(report)

def main():
    """Main analysis function"""
    # Load data
    data_path = '/clusterfs/jgi/scratch/science/mgs/nelli/roniya/IGV/bwa-env/Hisat2/test'
    df = load_and_clean_data(data_path)
    
    print("Data loaded successfully!")
    print(f"Shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Create visualizations
    print("\nCreating boxplot...")
    create_boxplot(df)
    
    print("Creating bar chart...")
    fig, summary_stats = create_barplot(df)
    
    # Perform statistical analysis
    print("Performing statistical analysis...")
    stats_results = perform_statistical_analysis(df)
    
    # Generate report
    report = generate_summary_report(df, stats_results, summary_stats)
    
    # Save report
    with open('viral_analysis_report.txt', 'w') as f:
        f.write(report)
    
    print("\nAnalysis complete!")
    print("Files generated:")
    print("- viral_copies_boxplot.png")
    print("- viral_copies_barplot.png") 
    print("- viral_analysis_report.txt")
    
    print("\n" + "="*50)
    print(report)

if __name__ == "__main__":
    main()
